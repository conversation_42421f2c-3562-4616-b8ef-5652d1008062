<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta content="text/html; charset=utf-8" http-equiv="content-type" />
	<title>CKEditor Samples &mdash; PHP Integration</title>
	<link type="text/css" rel="stylesheet" href="../sample.css" />
</head>
<body>
	<h1 class="samples">
		CKEditor Samples List for PHP
	</h1>
	<h2 class="samples">
		Basic Samples
	</h2>
	<ul class="samples">
		<li><a class="samples" href="replace.php">Replace existing textarea elements by code</a><br />
		Replacement of selected textarea elements with CKEditor instances by using a JavaScript call.</li>
		<li><a class="samples" href="replaceall.php">Replace all textarea elements by code</a><br />
		Replacement of all textarea elements with CKEditor instances by using a JavaScript call.</li>
		<li><a class="samples" href="standalone.php">Create CKEditor instances in PHP</a><br />
		Creating a CKEditor instance (no initial textarea element is required).</li>
	</ul>
	<h2 class="samples">
		Advanced Samples
	</h2>
	<ul class="samples">
		<li><a class="samples" href="advanced.php">Setting configuration options</a><br />
		Creating a CKEditor instance with custom configuration options.</li>
		<li><a class="samples" href="events.php">Listening to events</a><br />
		Creating event handlers.
		</li>
	</ul>
	<div id="footer">
		<hr />
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2011, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
