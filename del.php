<?php
// 会员管理系统 - 删除用户
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();

// 获取目标用户名
$target_username = $_GET['username'] ?? '';
if (empty($target_username)) {
    header('Location: user.php');
    exit();
}

// 防止删除自己
if ($target_username === $_SESSION['username']) {
    header('Location: user.php?error=不能删除自己的账户');
    exit();
}

// 验证用户是否存在
$user_stmt = safe_query($pdo, "SELECT * FROM tab WHERE username = ?", [$target_username]);
$target_user = $user_stmt->fetch();
if (!$target_user) {
    header('Location: user.php?error=用户不存在');
    exit();
}

$message = '';
$error = '';

// 处理删除请求
if ($_POST && isset($_POST['confirm_delete'])) {
    $confirm_username = trim($_POST['confirm_username'] ?? '');
    
    if ($confirm_username !== $target_username) {
        $error = '确认用户名不匹配，请重新输入！';
    } else {
        try {
            // 开始事务
            $pdo->beginTransaction();
            
            // 删除用户的所有内容
            $content_stmt = $pdo->prepare("DELETE FROM content WHERE username = ?");
            $content_stmt->execute([$target_username]);
            $deleted_content = $content_stmt->rowCount();
            
            // 删除用户账户
            $user_stmt = $pdo->prepare("DELETE FROM tab WHERE username = ?");
            $user_stmt->execute([$target_username]);
            $deleted_user = $user_stmt->rowCount();
            
            if ($deleted_user > 0) {
                // 提交事务
                $pdo->commit();
                $message = "用户 '$target_username' 已成功删除！同时删除了 $deleted_content 条相关内容。";
                
                // 记录删除日志
                error_log("管理员 {$_SESSION['username']} 删除了用户 $target_username，删除内容 $deleted_content 条");
            } else {
                $pdo->rollback();
                $error = '删除失败，用户可能已被其他管理员删除！';
            }
        } catch (Exception $e) {
            $pdo->rollback();
            $error = '删除失败：' . $e->getMessage();
            error_log("删除用户错误: " . $e->getMessage());
        }
    }
}

// 获取用户的内容统计
$content_count = 0;
$content_stmt = safe_query($pdo, "SELECT COUNT(*) FROM content WHERE username = ?", [$target_username]);
if ($content_stmt) {
    $content_count = $content_stmt->fetchColumn();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 删除用户</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 650px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .danger-icon {
            font-size: 5em;
            color: #dc3545;
            margin-bottom: 20px;
            animation: shake 2s infinite;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .title {
            color: #dc3545;
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 30px;
        }
        
        .user-info {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #dc3545;
        }
        
        .user-info h3 {
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .user-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            text-align: left;
        }
        
        .user-detail {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .user-detail .label {
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }
        
        .user-detail .value {
            color: #666;
            font-size: 16px;
        }
        
        .danger-warning {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            border: 2px solid #dc3545;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            color: #721c24;
        }
        
        .danger-warning h4 {
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .danger-warning ul {
            text-align: left;
            margin-left: 20px;
        }
        
        .danger-warning li {
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .content-stats {
            background: #fff3cd;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #ffc107;
        }
        
        .content-stats h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            text-align: center;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.7);
            padding: 15px;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #dc3545;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .confirmation-form {
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #dc3545;
            border-radius: 10px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #c82333;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
        
        .checkbox-container input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #dc3545;
        }
        
        .checkbox-container label {
            color: #dc3545;
            font-weight: bold;
            cursor: pointer;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }
        
        .btn-danger:hover {
            box-shadow: 0 15px 30px rgba(220, 53, 69, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 15px 30px rgba(108, 117, 125, 0.4);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 25px;
                margin: 20px;
            }
            
            .user-details, .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="danger-icon">💀</div>
        <h1 class="title">删除用户</h1>
        <p class="subtitle">此操作将永久删除用户及其所有数据</p>
        
        <div class="user-info">
            <h3>🎯 即将删除的用户</h3>
            <div class="user-details">
                <div class="user-detail">
                    <span class="label">用户名</span>
                    <span class="value"><?php echo safe_output($target_user['username']); ?></span>
                </div>
                <div class="user-detail">
                    <span class="label">权限等级</span>
                    <span class="value">
                        <?php if ($target_user['ok'] == 1): ?>
                            👑 管理员
                        <?php else: ?>
                            🎯 普通用户
                        <?php endif; ?>
                    </span>
                </div>
                <div class="user-detail">
                    <span class="label">注册时间</span>
                    <span class="value"><?php echo date('Y-m-d H:i', strtotime($target_user['regtime'])); ?></span>
                </div>
                <div class="user-detail">
                    <span class="label">用户ID</span>
                    <span class="value">#<?php echo $target_user['id']; ?></span>
                </div>
            </div>
        </div>
        
        <div class="content-stats">
            <h4>📊 将被删除的数据</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">用户账户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $content_count; ?></div>
                    <div class="stat-label">内容记录</div>
                </div>
            </div>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                ❌ <?php echo safe_output($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <div class="alert alert-success">
                ✅ <?php echo safe_output($message); ?>
            </div>
            <div class="button-group">
                <a href="user.php" class="btn btn-secondary">👥 返回用户列表</a>
                <a href="c.php" class="btn btn-secondary">🏛️ 返回控制台</a>
            </div>
        <?php else: ?>
            <div class="danger-warning">
                <h4>🚨 极度危险操作</h4>
                <ul>
                    <li><strong>用户账户将被永久删除</strong></li>
                    <li><strong>所有相关内容将被清空</strong></li>
                    <li><strong>删除后无法恢复任何数据</strong></li>
                    <li><strong>建议先备份重要数据</strong></li>
                    <li><strong>请三思而后行</strong></li>
                </ul>
            </div>
            
            <form method="POST" action="" class="confirmation-form">
                <div class="form-group">
                    <label for="confirm_username">请输入用户名以确认删除：</label>
                    <input type="text" id="confirm_username" name="confirm_username" required 
                           placeholder="输入 <?php echo safe_output($target_username); ?> 以确认"
                           autocomplete="off">
                </div>
                
                <div class="checkbox-container">
                    <input type="checkbox" id="confirm_checkbox" required>
                    <label for="confirm_checkbox">我理解此操作的后果，确认删除该用户</label>
                </div>
                
                <div class="button-group">
                    <button type="submit" name="confirm_delete" value="1" class="btn btn-danger" id="deleteBtn">
                        💀 确认删除
                    </button>
                    <a href="user.php" class="btn btn-secondary">❌ 取消操作</a>
                </div>
            </form>
        <?php endif; ?>
    </div>
    
    <script>
        const targetUsername = '<?php echo safe_output($target_username); ?>';
        const confirmInput = document.getElementById('confirm_username');
        const confirmCheckbox = document.getElementById('confirm_checkbox');
        const deleteBtn = document.getElementById('deleteBtn');
        
        // 检查表单状态
        function checkFormStatus() {
            const usernameMatch = confirmInput.value.trim() === targetUsername;
            const checkboxChecked = confirmCheckbox.checked;
            
            if (usernameMatch && checkboxChecked) {
                deleteBtn.style.opacity = '1';
                deleteBtn.style.pointerEvents = 'auto';
                deleteBtn.style.background = 'linear-gradient(135deg, #dc3545, #c82333)';
            } else {
                deleteBtn.style.opacity = '0.5';
                deleteBtn.style.pointerEvents = 'none';
                deleteBtn.style.background = '#ccc';
            }
        }
        
        // 监听输入变化
        confirmInput.addEventListener('input', checkFormStatus);
        confirmCheckbox.addEventListener('change', checkFormStatus);
        
        // 初始化状态
        checkFormStatus();
        
        // 表单提交前的最终确认
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = confirmInput.value.trim();
            const contentCount = <?php echo $content_count; ?>;
            
            if (username !== targetUsername) {
                e.preventDefault();
                alert('用户名确认不匹配！');
                confirmInput.focus();
                return false;
            }
            
            if (!confirmCheckbox.checked) {
                e.preventDefault();
                alert('请先勾选确认复选框！');
                return false;
            }
            
            const finalConfirm = confirm(
                `最终确认删除：\n\n` +
                `用户名：${targetUsername}\n` +
                `内容数量：${contentCount} 条\n` +
                `删除时间：${new Date().toLocaleString()}\n\n` +
                `此操作不可恢复！确定要删除吗？`
            );
            
            if (!finalConfirm) {
                e.preventDefault();
                return false;
            }
            
            // 显示删除进度
            deleteBtn.innerHTML = '🔄 删除中...';
            deleteBtn.disabled = true;
        });
        
        // 防止意外刷新
        window.addEventListener('beforeunload', function(e) {
            if (confirmInput.value.trim() || confirmCheckbox.checked) {
                e.preventDefault();
                e.returnValue = '您正在进行危险操作，确定要离开吗？';
                return e.returnValue;
            }
        });
    </script>
</body>
</html>
