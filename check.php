<?php
/**
 * 会员管理系统 - 内容查看页面
 * 深红色主题版本
 */

// 包含系统配置
require_once 'config.php';

// 检查用户登录状态
check_login();

$error = '';
$success = '';

// 获取当前用户信息
$current_user = get_user_info($_SESSION['user_id']);

// 获取要查看的用户ID
$target_user_id = $_SESSION['user_id']; // 默认查看自己的内容
$target_user = $current_user;

// 如果指定了用户ID，则查看指定用户的内容
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $target_user_id = intval($_GET['id']);
    $target_user = get_user_info($target_user_id);

    if (!$target_user) {
        $error = '指定的用户不存在！';
        $target_user_id = $_SESSION['user_id'];
        $target_user = $current_user;
    }
}

// 权限检查：普通用户只能查看自己的内容，管理员可以查看所有用户的内容
if ($_SESSION['ok'] != 1 && $target_user_id != $_SESSION['user_id']) {
    $error = '您没有权限查看其他用户的内容！';
    $target_user_id = $_SESSION['user_id'];
    $target_user = $current_user;
}

// 获取用户内容
try {
    $pdo = connectDB();

    // 获取用户的所有内容
    $stmt = safe_query($pdo,
        "SELECT * FROM content WHERE user_id = ? ORDER BY time DESC",
        [$target_user_id]
    );
    $user_contents = $stmt->fetchAll();

    // 如果是查看其他用户，还要获取通用内容
    $general_contents = [];
    if ($target_user_id != $_SESSION['user_id'] || empty($user_contents)) {
        $stmt = safe_query($pdo,
            "SELECT c.*, t.username FROM content c
             LEFT JOIN tab t ON c.user_id = t.id
             WHERE c.type = 'general'
             ORDER BY c.time DESC LIMIT 5"
        );
        $general_contents = $stmt->fetchAll();
    }

} catch (Exception $e) {
    $user_contents = [];
    $general_contents = [];
    $error = '获取内容失败：' . $e->getMessage();
    error_log("获取内容错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>查看内容</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .profile-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        .content-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        .profile-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            background-color: rgba(255, 255, 255, 0.15);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .value {
            font-size: 18px;
            font-weight: bold;
        }
        .status-admin {
            color: #ffd700;
            font-weight: bold;
        }
        .status-normal {
            color: #add8e6;
        }
        .content-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }
        .content-body {
            line-height: 1.6;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .content-time {
            margin-top: 15px;
            text-align: right;
            font-style: italic;
            opacity: 0.8;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        .action-btn {
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .action-btn:hover {
            background-color: #f2f2f2;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
        .nav-link {
            display: block;
            padding: 10px 0;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .nav-link:hover {
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>我的主页</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="#home" class="nav-link">个人主页</a>
            <a href="#edit-content" class="nav-link" onclick="window.location.href='u.php'">编辑内容</a>
            <a href="#clear-content" class="nav-link" onclick="window.location.href='udel.php'">清空内容</a>
            <a href="#print" class="nav-link" onclick="window.print()">打印内容</a>
            <a href="#copy" class="nav-link" onclick="copyContent()">复制内容</a>
            <?php if ($_SESSION['ok'] == 1): ?>
                <a href="#dashboard" class="nav-link" onclick="window.location.href='c.php'">返回控制台</a>
            <?php else: ?>
                <a href="#home" class="nav-link" onclick="window.location.href='e.php'">返回主页</a>
            <?php endif; ?>
        </div>
        <div class="main-content">
            <div class="profile-card">
                <h2>个人信息</h2>
                <div class="profile-info">
                    <div class="info-item">
                        <div class="label">用户名</div>
                        <div class="value"><?php echo htmlspecialchars($user_info['user']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="label">加入时间</div>
                        <div class="value">
                            <?php 
                                // 获取注册时间
                                $sql = "SELECT time FROM tab WHERE id = ?";
                                $stmt = $conn->prepare($sql);
                                $stmt->bind_param("i", $user_id);
                                $stmt->execute();
                                $result = $stmt->get_result();
                                $time_row = $result->fetch_assoc();
                                echo $time_row['time'];
                            ?>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="label">身份标识</div>
                        <div class="value">
                            <?php if ($user_info['ok'] == 1): ?>
                                <span class="status-admin">VIP管理员</span>
                            <?php else: ?>
                                <span class="status-normal">尊贵会员</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-card">
                <h2>专属内容</h2>
                <?php if ($content_info): ?>
                    <div class="content-title">
                        <?php echo htmlspecialchars($content_info['title']); ?>
                    </div>
                    <div class="content-body">
                        <?php echo $content_info['content']; ?>
                    </div>
                    <div class="content-time">
                        最后更新时间：<?php echo $content_info['time']; ?>
                    </div>
                <?php else: ?>
                    <p>您还没有添加任何内容。请<a href="u.php" style="color: #ffd700; text-decoration: underline;">点击这里</a>添加您的专属内容。</p>
                <?php endif; ?>
                
                <div class="action-buttons">
                    <button class="action-btn" onclick="window.print()">打印内容</button>
                    <button class="action-btn" onclick="copyContent()">复制内容</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        function copyContent() {
            const content = document.querySelector('.content-body');
            if (content) {
                const range = document.createRange();
                range.selectNodeContents(content);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
                
                try {
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    alert('内容已成功复制到剪贴板！');
                } catch (err) {
                    alert('复制失败，请手动复制内容。');
                }
            } else {
                alert('没有可复制的内容。');
            }
        }
    </script>
</body>
</html>
