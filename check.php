<?php
/**
 * 会员管理系统 - 内容查看页面
 * 深红色主题版本
 */

// 包含系统配置
require_once 'config.php';

// 检查用户登录状态
check_login();

$error = '';
$success = '';

// 获取当前用户信息
$current_user = get_user_info($_SESSION['user_id']);

// 获取要查看的用户ID
$target_user_id = $_SESSION['user_id']; // 默认查看自己的内容
$target_user = $current_user;

// 如果指定了用户ID，则查看指定用户的内容
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $target_user_id = intval($_GET['id']);
    $target_user = get_user_info($target_user_id);

    if (!$target_user) {
        $error = '指定的用户不存在！';
        $target_user_id = $_SESSION['user_id'];
        $target_user = $current_user;
    }
}

// 权限检查：普通用户只能查看自己的内容，管理员可以查看所有用户的内容
if ($_SESSION['ok'] != 1 && $target_user_id != $_SESSION['user_id']) {
    $error = '您没有权限查看其他用户的内容！';
    $target_user_id = $_SESSION['user_id'];
    $target_user = $current_user;
}

// 获取用户内容
$user_contents = [];
$general_contents = [];

try {
    $pdo = connectDB();

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'content'");
    if ($stmt->rowCount() == 0) {
        $error = '数据库表不存在，请先运行安装程序！';
    } else {
        // 获取用户的所有内容
        $stmt = safe_query($pdo,
            "SELECT * FROM content WHERE user_id = ? ORDER BY time DESC",
            [$target_user_id]
        );
        $user_contents = $stmt->fetchAll();

        // 如果是查看其他用户，还要获取通用内容
        if ($target_user_id != $_SESSION['user_id'] || empty($user_contents)) {
            // 检查是否有type字段
            $stmt = $pdo->query("SHOW COLUMNS FROM content LIKE 'type'");
            if ($stmt->rowCount() > 0) {
                $stmt = safe_query($pdo,
                    "SELECT c.*, t.username FROM content c
                     LEFT JOIN tab t ON c.user_id = t.id
                     WHERE c.type = 'general'
                     ORDER BY c.time DESC LIMIT 5"
                );
            } else {
                // 如果没有type字段，获取所有内容
                $stmt = safe_query($pdo,
                    "SELECT c.*, t.username FROM content c
                     LEFT JOIN tab t ON c.user_id = t.id
                     ORDER BY c.time DESC LIMIT 5"
                );
            }
            $general_contents = $stmt->fetchAll();
        }
    }

} catch (Exception $e) {
    $error = '获取内容失败：' . $e->getMessage();
    error_log("获取内容错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 查看内容</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .user-info-section {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-avatar {
            font-size: 4em;
            margin-bottom: 15px;
        }

        .user-name {
            font-size: 2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .user-badge {
            display: inline-block;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }

        .badge-admin {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
        }

        .badge-user {
            background: linear-gradient(135deg, #87CEEB, #4682B4);
            color: white;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 1.8em;
            margin-bottom: 25px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .content-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .content-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #FFD700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .content-meta {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 10px;
        }

        .content-body {
            line-height: 1.8;
            font-size: 1.1em;
        }

        .content-body h1, .content-body h2, .content-body h3 {
            color: #FFD700;
            margin: 20px 0 10px 0;
        }

        .content-body p {
            margin-bottom: 15px;
        }

        .content-body ul, .content-body ol {
            margin: 15px 0 15px 30px;
        }

        .no-content {
            text-align: center;
            padding: 60px 20px;
            opacity: 0.7;
        }

        .no-content h3 {
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .no-content p {
            font-size: 1.1em;
            margin-bottom: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }

        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            padding: 12px 25px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .user-info-section, .content-section {
                padding: 20px;
            }
        }
    </style>
</head>
    <div class="header">
        <div class="header-content">
            <h1>👁️ 查看内容</h1>
            <div class="nav-links">
                <?php if ($current_user['ok'] == 1): ?>
                    <a href="c.php" class="nav-link">返回控制台</a>
                    <a href="user.php" class="nav-link">会员管理</a>
                <?php else: ?>
                    <a href="e.php" class="nav-link">返回主页</a>
                <?php endif; ?>
                <a href="u.php<?php echo $target_user_id != $_SESSION['user_id'] ? '?id=' . $target_user_id : ''; ?>" class="nav-link">编辑内容</a>
                <a href="close.php" class="nav-link">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 错误提示 -->
        <?php if ($error): ?>
            <div class="alert alert-error">
                ⚠️ <?php echo safe_output($error); ?>
                <?php if (strpos($error, '数据库表不存在') !== false): ?>
                    <br><br>
                    <a href="install/index.php" class="btn">🔧 运行安装程序</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- 用户信息 -->
        <div class="user-info-section">
            <div class="user-avatar">
                <?php echo $target_user['ok'] == 1 ? '👑' : '👤'; ?>
            </div>
            <div class="user-name">
                <?php echo safe_output($target_user['username']); ?>
            </div>
            <div>
                <span class="user-badge <?php echo $target_user['ok'] == 1 ? 'badge-admin' : 'badge-user'; ?>">
                    <?php echo $target_user['ok'] == 1 ? '👑 VIP管理员' : '👤 尊贵会员'; ?>
                </span>
                <span class="user-badge <?php echo $target_user['status'] == 1 ? 'badge-user' : 'badge-admin'; ?>">
                    <?php echo $target_user['status'] == 1 ? '✅ 账户正常' : '🚫 账户禁用'; ?>
                </span>
            </div>
            <div style="margin-top: 15px; opacity: 0.8;">
                加入时间：<?php echo $target_user['time']; ?>
                <?php if ($target_user['last_login']): ?>
                    | 最后登录：<?php echo $target_user['last_login']; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- 用户内容 -->
        <?php if (!empty($user_contents)): ?>
        <div class="content-section">
            <h2 class="section-title">
                📝 <?php echo $target_user_id == $_SESSION['user_id'] ? '我的内容' : safe_output($target_user['username']) . ' 的内容'; ?>
            </h2>

            <?php foreach ($user_contents as $content): ?>
            <div class="content-item">
                <div class="content-title">
                    <?php echo safe_output($content['title']); ?>
                </div>
                <div class="content-meta">
                    类型：<?php echo isset($content['type']) ? ($content['type'] == 'general' ? '通用内容' : '个人内容') : '个人内容'; ?> |
                    创建时间：<?php echo $content['time']; ?>
                    <?php if (isset($content['update_time']) && $content['update_time'] != $content['time']): ?>
                        | 更新时间：<?php echo $content['update_time']; ?>
                    <?php endif; ?>
                </div>
                <div class="content-body">
                    <?php echo $content['content']; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- 通用内容 -->
        <?php if (!empty($general_contents)): ?>
        <div class="content-section">
            <h2 class="section-title">📰 系统公告</h2>

            <?php foreach ($general_contents as $content): ?>
            <div class="content-item">
                <div class="content-title">
                    <?php echo safe_output($content['title']); ?>
                </div>
                <div class="content-meta">
                    发布者：<?php echo safe_output($content['username']); ?> |
                    发布时间：<?php echo $content['time']; ?>
                </div>
                <div class="content-body">
                    <?php echo $content['content']; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- 无内容提示 -->
        <?php if (empty($user_contents) && empty($general_contents) && !$error): ?>
        <div class="content-section">
            <div class="no-content">
                <h3>📝 暂无内容</h3>
                <p><?php echo $target_user_id == $_SESSION['user_id'] ? '您还没有创建任何内容' : '该用户还没有创建任何内容'; ?></p>
                <?php if ($target_user_id == $_SESSION['user_id'] || $_SESSION['ok'] == 1): ?>
                    <a href="u.php<?php echo $target_user_id != $_SESSION['user_id'] ? '?id=' . $target_user_id : ''; ?>" class="btn">
                        ✏️ 创建内容
                    </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- 操作按钮 -->
        <?php if (!empty($user_contents) || !empty($general_contents)): ?>
        <div class="action-buttons">
            <button class="btn" onclick="window.print()">
                🖨️ 打印页面
            </button>
            <button class="btn" onclick="copyAllContent()">
                📋 复制内容
            </button>
            <?php if ($target_user_id == $_SESSION['user_id'] || $_SESSION['ok'] == 1): ?>
            <a href="u.php<?php echo $target_user_id != $_SESSION['user_id'] ? '?id=' . $target_user_id : ''; ?>" class="btn">
                ✏️ 编辑内容
            </a>
            <?php endif; ?>
            <?php if ($_SESSION['ok'] == 1): ?>
            <a href="udel.php?id=<?php echo $target_user_id; ?>" class="btn btn-secondary"
               onclick="return confirm('确定要清空该用户的所有内容吗？此操作不可撤销！')">
                🗑️ 清空内容
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // 复制所有内容
        function copyAllContent() {
            const contentElements = document.querySelectorAll('.content-body');
            if (contentElements.length > 0) {
                let allContent = '';
                contentElements.forEach((element, index) => {
                    const title = element.parentElement.querySelector('.content-title').textContent;
                    allContent += `${title}\n${'='.repeat(title.length)}\n\n`;
                    allContent += element.textContent.trim() + '\n\n';
                });

                navigator.clipboard.writeText(allContent).then(() => {
                    alert('✅ 所有内容已成功复制到剪贴板！');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = allContent;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        alert('✅ 所有内容已成功复制到剪贴板！');
                    } catch (err) {
                        alert('❌ 复制失败，请手动复制内容。');
                    }
                    document.body.removeChild(textArea);
                });
            } else {
                alert('📝 没有可复制的内容。');
            }
        }

        // 打印样式优化
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
            document.body.style.color = 'black';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.background = '';
            document.body.style.color = '';
        });
    </script>
</body>
</html>
