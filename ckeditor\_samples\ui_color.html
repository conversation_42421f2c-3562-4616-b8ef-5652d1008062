<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>UI Color Picker &mdash; CKEditor Sample</title>
	<meta content="text/html; charset=utf-8" http-equiv="content-type" />
	<script type="text/javascript" src="../ckeditor.js"></script>
	<script src="sample.js" type="text/javascript"></script>
	<link href="sample.css" rel="stylesheet" type="text/css" />
</head>
<body>
	<h1 class="samples">
		CKEditor Sample &mdash; UI Color Picker
	</h1>
	<div class="description">
	<p>
		This sample shows how to automatically replace <code>&lt;textarea&gt;</code> elements
		with a CKEditor instance with an option to change the color of its user interface.
	</p>
	<h2 class="samples">Setting the User Interface Color</h2>
	<p>
	To specify the color of the user interface, set the <code>uiColor</code> property:
	</p>
	<pre class="samples">CKEDITOR.replace( '<em>textarea_id</em>',
	{
		<strong>uiColor: '#EE0000'</strong>
	});</pre>
	<p>
		Note that <code><em>textarea_id</em></code> in the code above is the <code>id</code> attribute of
		the <code>&lt;textarea&gt;</code> element to be replaced.
	</p>
	<h2 class="samples">Enabling the Color Picker</h2>
	<p>
		If the <strong>uicolor</strong> plugin along with the dedicated <strong>UIColor</strong>
		toolbar button is added to CKEditor, the user will also be able to pick the color of the
		UI from the color palette available in the <strong>UI Color Picker</strong> dialog window.
	</p>
	<p>
		To insert a CKEditor instance with the <strong>uicolor</strong> plugin enabled,
		use the following JavaScript call:
	</p>
	<pre class="samples">CKEDITOR.replace( '<em>textarea_id</em>',
	{
		<strong>extraPlugins : 'uicolor',</strong>
		toolbar : [ [ 'Bold', 'Italic' ], [ <strong>'UIColor'</strong> ] ]
	});</pre>
	</div>
	<!-- This <div> holds alert messages to be display in the sample page. -->
	<div id="alerts">
		<noscript>
			<p>
				<strong>CKEditor requires JavaScript to run</strong>. In a browser with no JavaScript
				support, like yours, you should still see the contents (HTML data) and you should
				be able to edit it normally, without a rich editor interface.
			</p>
		</noscript>
	</div>
	<p>
		Click the <strong>UI Color Picker</strong> button to test your color preferences at runtime.
	</p>
	<p>
		The first editor instance includes the <strong>UI Color Picker</strong> toolbar button,
		but the default UI color is not defined, so the editor uses the skin color.
	</p>
	<form action="sample_posteddata.php" method="post">
	<p>
		<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		<script type="text/javascript">
		//<![CDATA[

			// Replace the <textarea id="editor"> with an CKEditor
			// instance, using default configurations.
			CKEDITOR.replace( 'editor1',
				{
					extraPlugins : 'uicolor',
					toolbar :
					[
						[ 'Bold', 'Italic', '-', 'NumberedList', 'BulletedList', '-', 'Link', 'Unlink' ],
						[ 'UIColor' ]
					]
				});

		//]]>
		</script>
	</p>
	<p>
		The second editor instance includes the <strong>UI Color Picker</strong> toolbar button. The
		default UI color was defined, so the skin color is not used.
	</p>
	<p>
		<textarea cols="80" id="editor2" name="editor2" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		<script type="text/javascript">
		//<![CDATA[

			// Replace the <textarea id="editor"> with an CKEditor
			// instance, using default configurations.
			CKEDITOR.replace( 'editor2',
				{
					extraPlugins : 'uicolor',
					uiColor: '#14B8C4',
					toolbar :
					[
						[ 'Bold', 'Italic', '-', 'NumberedList', 'BulletedList', '-', 'Link', 'Unlink' ],
						[ 'UIColor' ]
					]
				} );

		//]]>
		</script>
	</p>
	<p>
		<input type="submit" value="Submit" />
	</p>
	</form>
	<div id="footer">
		<hr />
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2011, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
