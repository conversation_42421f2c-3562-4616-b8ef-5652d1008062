<?php
// 系统诊断页面
session_start();

echo "<h1>🔍 系统诊断</h1>";
echo "<style>body{font-family:Arial;padding:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;}</style>";

// 1. 检查PHP版本
echo "<h2>📋 PHP环境</h2>";
echo "PHP版本: " . PHP_VERSION;
if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
    echo " <span class='ok'>✅ 支持</span><br>";
} else {
    echo " <span class='error'>❌ 版本过低</span><br>";
}

// 2. 检查必要扩展
$extensions = ['pdo', 'pdo_mysql', 'mysqli', 'session'];
echo "<h3>PHP扩展:</h3>";
foreach ($extensions as $ext) {
    echo "$ext: ";
    if (extension_loaded($ext)) {
        echo "<span class='ok'>✅ 已加载</span><br>";
    } else {
        echo "<span class='error'>❌ 未加载</span><br>";
    }
}

// 3. 检查配置文件
echo "<h2>📁 文件检查</h2>";
$files = ['config.php', 'index.php', 'c.php', 'install.lock'];
foreach ($files as $file) {
    echo "$file: ";
    if (file_exists($file)) {
        echo "<span class='ok'>✅ 存在</span><br>";
    } else {
        echo "<span class='error'>❌ 不存在</span><br>";
    }
}

// 4. 检查数据库连接
echo "<h2>🗄️ 数据库连接</h2>";
try {
    require_once 'config.php';
    $pdo = connectDB();
    echo "数据库连接: <span class='ok'>✅ 成功</span><br>";
    
    // 检查表是否存在
    $tables = ['tab', 'content'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "表 $table: <span class='ok'>✅ 存在 ($count 条记录)</span><br>";
        } catch (Exception $e) {
            echo "表 $table: <span class='error'>❌ 不存在</span><br>";
        }
    }
    
} catch (Exception $e) {
    echo "数据库连接: <span class='error'>❌ 失败 - " . $e->getMessage() . "</span><br>";
}

// 5. 检查Session状态
echo "<h2>🔐 Session状态</h2>";
echo "Session ID: " . session_id() . "<br>";
echo "登录状态: ";
if (isset($_SESSION['username'])) {
    echo "<span class='ok'>✅ 已登录</span><br>";
    echo "用户名: " . htmlspecialchars($_SESSION['username']) . "<br>";
    echo "权限级别: " . ($_SESSION['ok'] == 1 ? '<span class="ok">管理员</span>' : '<span class="warning">普通用户</span>') . "<br>";
} else {
    echo "<span class='warning'>⚠️ 未登录</span><br>";
}

// 6. 检查目录权限
echo "<h2>📂 目录权限</h2>";
$dirs = ['backups', 'ckeditor'];
foreach ($dirs as $dir) {
    echo "$dir/: ";
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span class='ok'>✅ 可写</span><br>";
        } else {
            echo "<span class='warning'>⚠️ 只读</span><br>";
        }
    } else {
        echo "<span class='error'>❌ 不存在</span><br>";
    }
}

// 7. 快速操作链接
echo "<h2>🚀 快速操作</h2>";
echo "<a href='index.php' style='color:blue;'>🔑 登录页面</a><br>";
echo "<a href='c.php' style='color:blue;'>🏛️ 管理控制台</a><br>";
echo "<a href='e.php' style='color:blue;'>🏠 用户中心</a><br>";
echo "<a href='install/' style='color:blue;'>🔧 重新安装</a><br>";

// 8. 系统信息
echo "<h2>ℹ️ 系统信息</h2>";
echo "服务器时间: " . date('Y-m-d H:i:s') . "<br>";
echo "服务器软件: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "当前目录: " . getcwd() . "<br>";

// 9. 错误日志检查
echo "<h2>📝 错误日志</h2>";
if (file_exists('error.log')) {
    $log_content = file_get_contents('error.log');
    if (!empty($log_content)) {
        echo "<pre style='background:#f5f5f5;padding:10px;max-height:200px;overflow:auto;'>";
        echo htmlspecialchars(substr($log_content, -1000)); // 显示最后1000字符
        echo "</pre>";
    } else {
        echo "<span class='ok'>✅ 无错误记录</span><br>";
    }
} else {
    echo "<span class='ok'>✅ 无错误日志文件</span><br>";
}

echo "<hr>";
echo "<p><strong>💡 提示：</strong></p>";
echo "<ul>";
echo "<li>如果未登录，请先访问 <a href='index.php'>登录页面</a></li>";
echo "<li>管理员账户：admin / admin123</li>";
echo "<li>如果数据库连接失败，请检查 config.php 配置</li>";
echo "<li>如果表不存在，请重新运行安装程序</li>";
echo "</ul>";
?>
