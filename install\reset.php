<?php
// 数据库重置工具
// 用于清理安装失败后的残留数据

// 检查是否已经安装
if (file_exists('../install.lock')) {
    die('系统已安装！如需重置，请先删除 install.lock 文件。');
}

$error = '';
$success = '';

if ($_POST) {
    $host = $_POST['db_host'] ?? 'localhost';
    $username = $_POST['db_username'] ?? 'root';
    $password = $_POST['db_password'] ?? '';
    $dbname = $_POST['db_name'] ?? 'member_system';
    
    try {
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 删除数据库（如果存在）
        $pdo->exec("DROP DATABASE IF EXISTS `$dbname`");
        
        $success = "数据库 '$dbname' 已成功重置！现在可以重新运行安装程序。";
        
    } catch (Exception $e) {
        $error = '重置失败：' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库重置工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .reset-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .reset-header h1 {
            color: #dc3545;
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .reset-header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            color: #856404;
        }
        
        .warning h3 {
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.2);
        }
        
        .btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #8B0000;
            text-decoration: none;
            font-weight: bold;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h1>🔄 数据库重置</h1>
            <p>清理安装失败后的残留数据</p>
        </div>
        
        <div class="warning">
            <h3>⚠️ 危险操作</h3>
            <p>此操作将<strong>完全删除</strong>指定的数据库及其所有数据！</p>
            <p>请确保您了解此操作的后果。</p>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">✅ <?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if (!$success): ?>
            <form method="POST">
                <div class="form-group">
                    <label for="db_host">数据库主机</label>
                    <input type="text" id="db_host" name="db_host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="db_username">数据库用户名</label>
                    <input type="text" id="db_username" name="db_username" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="db_password">数据库密码</label>
                    <input type="password" id="db_password" name="db_password" value="111111">
                </div>
                
                <div class="form-group">
                    <label for="db_name">要重置的数据库名</label>
                    <input type="text" id="db_name" name="db_name" value="member_system" required>
                </div>
                
                <button type="submit" class="btn" onclick="return confirm('确定要删除数据库吗？此操作不可恢复！')">
                    🗑️ 重置数据库
                </button>
            </form>
        <?php endif; ?>
        
        <div class="links">
            <a href="index.php">🔧 返回安装</a>
            <a href="../">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>
