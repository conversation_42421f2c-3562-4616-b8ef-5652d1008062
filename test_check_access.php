<?php
// 测试 check.php 页面访问权限
session_start();
require_once 'config.php';

echo "<h1>🔍 check.php 访问权限测试</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .success { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; color: #2e7d32; }
    .error { background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0; color: #c62828; }
    .warning { background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; color: #856404; }
    table { border-collapse: collapse; width: 100%; margin: 15px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background: #f2f2f2; font-weight: bold; }
    .btn { background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 2px; display: inline-block; }
    .btn:hover { background: #005a8b; }
</style>";

// 检查登录状态
if (!isset($_SESSION['username'])) {
    echo "<div class='error'>❌ 未登录！请先登录系统。</div>";
    echo "<a href='index.php' class='btn'>🔑 前往登录</a>";
    exit();
}

echo "<div class='info'>";
echo "<h2>📋 当前登录信息</h2>";
echo "<strong>用户名：</strong>" . htmlspecialchars($_SESSION['username']) . "<br>";
echo "<strong>权限级别：</strong>" . ($_SESSION['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . " (ok=" . $_SESSION['ok'] . ")<br>";
echo "<strong>用户ID：</strong>" . ($_SESSION['user_id'] ?? '未知') . "<br>";
echo "<strong>登录时间：</strong>" . (isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : '未知') . "<br>";
echo "</div>";

try {
    $pdo = connectDB();
    
    // 获取所有用户
    $users = $pdo->query("SELECT * FROM tab ORDER BY id")->fetchAll();
    
    echo "<h2>👥 系统用户列表</h2>";
    echo "<table>";
    echo "<tr><th>ID</th><th>用户名</th><th>权限</th><th>注册时间</th><th>访问测试</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td>" . ($user['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . "</td>";
        echo "<td>" . $user['regtime'] . "</td>";
        echo "<td>";
        
        // 检查当前用户是否能访问这个用户的内容
        if ($_SESSION['ok'] == 1) {
            // 管理员可以访问所有用户
            echo "<span style='color: green;'>✅ 可访问</span> ";
            echo "<a href='check.php?username=" . urlencode($user['username']) . "' class='btn' target='_blank'>👁️ 查看内容</a>";
        } else {
            // 普通用户只能访问自己的
            if ($user['username'] === $_SESSION['username']) {
                echo "<span style='color: green;'>✅ 可访问（自己）</span> ";
                echo "<a href='check.php?username=" . urlencode($user['username']) . "' class='btn' target='_blank'>👁️ 查看内容</a>";
            } else {
                echo "<span style='color: orange;'>⚠️ 受限制</span> ";
                echo "<a href='check.php?username=" . urlencode($user['username']) . "' class='btn' target='_blank'>🔒 尝试访问</a>";
            }
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 特别测试用户111
    echo "<h2>🎯 用户111访问测试</h2>";
    $user111 = $pdo->prepare("SELECT * FROM tab WHERE username = ?");
    $user111->execute(['111']);
    $user111_data = $user111->fetch();
    
    if ($user111_data) {
        echo "<div class='success'>";
        echo "<strong>✅ 用户111存在</strong><br>";
        echo "ID: " . $user111_data['id'] . "<br>";
        echo "权限: " . ($user111_data['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . "<br>";
        echo "注册时间: " . $user111_data['regtime'] . "<br>";
        echo "</div>";
        
        if ($_SESSION['ok'] == 1) {
            echo "<div class='success'>";
            echo "<strong>🎉 您是管理员，可以访问用户111的内容！</strong><br>";
            echo "<a href='check.php?username=111' class='btn' target='_blank' style='font-size: 16px; padding: 12px 24px;'>🚀 查看用户111的内容</a>";
            echo "</div>";
        } else {
            if ($_SESSION['username'] === '111') {
                echo "<div class='success'>";
                echo "<strong>✅ 您就是用户111，可以查看自己的内容！</strong><br>";
                echo "<a href='check.php?username=111' class='btn' target='_blank'>👁️ 查看我的内容</a>";
                echo "</div>";
            } else {
                echo "<div class='warning'>";
                echo "<strong>⚠️ 您是普通用户，无法查看其他用户的内容</strong><br>";
                echo "访问 check.php?username=111 会自动重定向到您自己的内容<br>";
                echo "<a href='check.php?username=111' class='btn' target='_blank'>🔒 尝试访问（会重定向）</a>";
                echo "</div>";
            }
        }
    } else {
        echo "<div class='error'>";
        echo "<strong>❌ 用户111不存在</strong><br>";
        echo "需要先创建这个用户才能测试访问<br>";
        echo "<a href='test_users.php' class='btn'>➕ 创建用户111</a>";
        echo "</div>";
    }
    
    // 检查用户111的内容
    echo "<h2>📄 用户111的内容状态</h2>";
    if ($user111_data) {
        // 检查通用内容
        $general_content = $pdo->query("SELECT * FROM content WHERE type = 'general' ORDER BY updated_time DESC LIMIT 1")->fetch();
        echo "<div class='info'>";
        echo "<strong>📢 系统公告：</strong>";
        if ($general_content) {
            echo "存在（最后更新：" . $general_content['updated_time'] . "）";
        } else {
            echo "暂无";
        }
        echo "</div>";
        
        // 检查个人内容
        $personal_content = $pdo->prepare("SELECT * FROM content WHERE username = ? AND type = 'personal' ORDER BY updated_time DESC LIMIT 1");
        $personal_content->execute(['111']);
        $personal_data = $personal_content->fetch();
        
        echo "<div class='info'>";
        echo "<strong>🎯 用户111的专属内容：</strong>";
        if ($personal_data) {
            echo "存在（最后更新：" . $personal_data['updated_time'] . "）";
        } else {
            echo "暂无";
        }
        echo "</div>";
        
        if ($_SESSION['ok'] == 1) {
            echo "<div class='success'>";
            echo "<strong>管理员操作：</strong><br>";
            echo "<a href='u.php?username=111' class='btn'>📝 编辑用户111的内容</a> ";
            echo "<a href='check.php?username=111' class='btn'>👁️ 查看用户111的内容</a>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库错误：" . $e->getMessage() . "</div>";
}

echo "<hr>";
echo "<h2>🔗 快速操作</h2>";
echo "<a href='check.php' class='btn'>👁️ 查看我的内容</a> ";
echo "<a href='c.php' class='btn'>🏛️ 管理控制台</a> ";
echo "<a href='user.php' class='btn'>👥 用户管理</a> ";
echo "<a href='close.php' class='btn'>🚪 退出登录</a>";

echo "<hr>";
echo "<h2>💡 权限说明</h2>";
echo "<div class='info'>";
echo "<strong>管理员权限 (ok=1)：</strong><br>";
echo "• ✅ 可以查看任何用户的内容<br>";
echo "• ✅ 可以编辑任何用户的内容<br>";
echo "• ✅ 可以访问所有管理功能<br><br>";

echo "<strong>普通用户权限 (ok=2)：</strong><br>";
echo "• ✅ 只能查看自己的内容<br>";
echo "• ❌ 无法查看其他用户的内容<br>";
echo "• ❌ 访问其他用户内容时会自动重定向到自己的内容<br>";
echo "</div>";
?>
