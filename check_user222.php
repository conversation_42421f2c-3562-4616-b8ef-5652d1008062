<?php
// 开启会话
session_start();

// 检查用户是否登录且为管理员
if (!isset($_SESSION['user_id']) || $_SESSION['ok'] != 1) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 获取用户ID
if (!isset($_GET['id'])) {
    header("Location: user.php");
    exit;
}

$user_id = intval($_GET['id']);

// 获取用户信息
$sql = "SELECT id, user FROM tab WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

// 如果用户不存在，返回会员管理页面
if ($result->num_rows == 0) {
    header("Location: user.php");
    exit;
}

$user_info = $result->fetch_assoc();

// 获取用户内容
$sql = "SELECT title, content, time FROM content WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$content_info = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>查看用户内容</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .profile-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        .content-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        .profile-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .value {
            font-size: 18px;
            font-weight: bold;
        }
        .content-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 10px;
        }
        .content-body {
            line-height: 1.6;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        .content-time {
            margin-top: 15px;
            text-align: right;
            font-style: italic;
            opacity: 0.8;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        .action-btn {
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .action-btn:hover {
            background-color: #f2f2f2;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
        .nav-link {
            display: block;
            padding: 10px 0;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .nav-link:hover {
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>查看用户内容</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="#view-content" class="nav-link">查看内容</a>
            <a href="#members" class="nav-link" onclick="window.location.href='user.php'">会员列表</a>
            <a href="#dashboard" class="nav-link" onclick="window.location.href='c.php'">返回控制台</a>
        </div>
        <div class="main-content">
            <div class="profile-card">
                <h2>用户信息</h2>
                <div class="profile-info">
                    <div class="info-item">
                        <div class="label">用户名</div>
                        <div class="value"><?php echo htmlspecialchars($user_info['user']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="label">用户ID</div>
                        <div class="value"><?php echo $user_info['id']; ?></div>
                    </div>
                    <div class="info-item">
                        <div class="label">访问时间</div>
                        <div class="value"><?php echo date('Y-m-d H:i:s'); ?></div>
                    </div>
                </div>
            </div>
            
            <div class="content-card">
                <h2>用户内容</h2>
                <?php if ($content_info): ?>
                    <div class="content-title">
                        <?php echo htmlspecialchars($content_info['title']); ?>
                    </div>
                    <div class="content-body">
                        <?php echo $content_info['content']; ?>
                    </div>
                    <div class="content-time">
                        最后更新时间：<?php echo $content_info['time']; ?>
                    </div>
                <?php else: ?>
                    <p>该用户还没有添加任何内容。</p>
                <?php endif; ?>
                
                <div class="action-buttons">
                    <button class="action-btn" onclick="window.print()">打印内容</button>
                    <button class="action-btn" onclick="copyContent()">复制内容</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        function copyContent() {
            const content = document.querySelector('.content-body');
            if (content) {
                const range = document.createRange();
                range.selectNodeContents(content);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
                
                try {
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    alert('内容已成功复制到剪贴板！');
                } catch (err) {
                    alert('复制失败，请手动复制内容。');
                }
            } else {
                alert('没有可复制的内容。');
            }
        }
    </script>
</body>
</html>