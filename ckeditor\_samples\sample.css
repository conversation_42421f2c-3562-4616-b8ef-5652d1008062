/*
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

html, body, h1, h2, h3, h4, h5, h6, div, span, blockquote, p, address, form, fieldset, img, ul, ol, dl, dt, dd, li, hr, table, td, th, strong, em, sup, sub, dfn, ins, del, q, cite, var, samp, code, kbd, tt, pre {
	line-height: 1.5em;
}

body {
	padding:10px 30px;
}

input, textarea, select, option, optgroup, button, td, th {
	font-size: 100%;
}

pre,
code,
kbd,
samp,
tt{
  font-family: monospace,monospace;
  font-size: 1em;
}

h1.samples {
  color:#0782C1;
  font-size:200%;
  font-weight:normal;
  margin: 0;
  padding: 0;
}

h2.samples {
  color:#000000;
  font-size:130%;
  margin: 0;
  padding: 0;
}

p, blockquote, address, form, pre, dl, h1.samples, h2.samples {
	margin-bottom:15px;
}

ul.samples {
	margin-bottom:15px;
}

.clear {
	clear:both;
}

fieldset
{
	margin: 0;
	padding: 10px;
}

body, input, textarea {
	color: #333333;
	font-family: Arial, Helvetica, sans-serif;
}

body {
	font-size: 75%;
}

a.samples {
	color:#189DE1;
	text-decoration:none;
}

a.samples:hover {
  text-decoration:underline;
}

form
{
	margin: 0;
	padding: 0;
}

pre.samples
{
	background-color: #F7F7F7;
	border: 1px solid #D7D7D7;
	overflow: auto;
	padding: 0.25em;
}

#alerts
{
	color: Red;
}

#footer hr
{
	margin: 10px 0 15px 0;
	height: 1px;
	border: solid 1px gray;
	border-bottom: none;
}

#footer p
{
	margin: 0 10px 10px 10px;
	float: left;
}

#footer #copy
{
	float: right;
}

#outputSample
{
	width: 100%;
	table-layout: fixed;
}

#outputSample thead th
{
	color: #dddddd;
	background-color: #999999;
	padding: 4px;
	white-space: nowrap;
}

#outputSample tbody th
{
	vertical-align: top;
	text-align: left;
}

#outputSample pre
{
	margin: 0;
	padding: 0;
	white-space: pre; /* CSS2 */
	white-space: -moz-pre-wrap; /* Mozilla*/
	white-space: -o-pre-wrap; /* Opera 7 */
	white-space: pre-wrap; /* CSS 2.1 */
	white-space: pre-line; /* CSS 3 (and 2.1 as well, actually) */
	word-wrap: break-word; /* IE */
}

.description {
	border: 1px dotted #B7B7B7;
	margin-bottom: 10px;
	padding: 10px 10px 0;
}

label {
	display: block;
	margin-bottom:6px;
}

.cke_dialog label
{
	display: inline;
	margin-bottom: auto;
}
