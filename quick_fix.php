<?php
// 快速权限修复
session_start();

echo "<h1>⚡ 快速权限修复</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #8B0000, #DC143C); color: white; }
    .panel { background: rgba(255,255,255,0.95); color: #333; padding: 30px; border-radius: 15px; margin: 20px 0; }
    .btn { background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 5px; display: inline-block; border: none; cursor: pointer; font-weight: bold; }
    .btn:hover { background: #005a8b; transform: translateY(-2px); }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    .btn-warning { background: #ffc107; color: #212529; }
    .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
</style>";

echo "<div class='panel'>";

// 处理修复请求
if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        require_once 'config.php';
        $pdo = connectDB();
        
        if ($action === 'fix_admin') {
            // 修复admin账户的Session
            $stmt = $pdo->prepare("SELECT * FROM tab WHERE username = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch();
            
            if ($admin) {
                $_SESSION['username'] = $admin['username'];
                $_SESSION['ok'] = $admin['ok'];
                $_SESSION['user_id'] = $admin['id'];
                $_SESSION['login_time'] = time();
                
                echo "<div class='success'>";
                echo "✅ admin账户Session已修复！<br>";
                echo "用户名: " . $admin['username'] . "<br>";
                echo "权限: " . ($admin['ok'] == 1 ? '管理员' : '普通用户') . "<br>";
                echo "</div>";
                
                echo "<a href='user.php' class='btn btn-success'>🚀 立即访问用户管理</a>";
                echo "<a href='c.php' class='btn'>🏛️ 管理控制台</a>";
                
            } else {
                echo "<div class='error'>❌ admin账户不存在！</div>";
            }
            
        } elseif ($action === 'clear_session') {
            // 清除Session
            session_destroy();
            session_start();
            echo "<div class='success'>✅ Session已清除！请重新登录。</div>";
            echo "<a href='index.php' class='btn btn-warning'>🔑 前往登录</a>";
            
        } elseif ($action === 'check_current') {
            // 检查当前用户
            if (isset($_SESSION['username'])) {
                $stmt = $pdo->prepare("SELECT * FROM tab WHERE username = ?");
                $stmt->execute([$_SESSION['username']]);
                $user = $stmt->fetch();
                
                if ($user) {
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['ok'] = $user['ok'];
                    $_SESSION['user_id'] = $user['id'];
                    
                    echo "<div class='success'>";
                    echo "✅ 当前用户Session已同步！<br>";
                    echo "用户名: " . $user['username'] . "<br>";
                    echo "权限: " . ($user['ok'] == 1 ? '管理员' : '普通用户') . "<br>";
                    echo "</div>";
                    
                    if ($user['ok'] == 1) {
                        echo "<a href='user.php' class='btn btn-success'>🚀 访问用户管理</a>";
                    } else {
                        echo "<div class='error'>❌ 您不是管理员，无法访问用户管理页面</div>";
                        echo "<a href='e.php' class='btn'>🏠 用户中心</a>";
                    }
                } else {
                    echo "<div class='error'>❌ 用户不存在于数据库中</div>";
                }
            } else {
                echo "<div class='error'>❌ 未登录</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 错误: " . $e->getMessage() . "</div>";
    }
    
} else {
    // 显示修复选项
    echo "<h2>🔧 选择修复方案</h2>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<h3>方案1：修复admin账户Session</h3>";
    echo "<p>如果您应该是admin管理员，但Session数据有问题</p>";
    echo "<form method='POST' style='display:inline;'>";
    echo "<input type='hidden' name='action' value='fix_admin'>";
    echo "<button type='submit' class='btn btn-success'>🔧 修复admin Session</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<h3>方案2：同步当前用户数据</h3>";
    echo "<p>如果您已登录但权限数据不同步</p>";
    echo "<form method='POST' style='display:inline;'>";
    echo "<input type='hidden' name='action' value='check_current'>";
    echo "<button type='submit' class='btn btn-warning'>🔄 同步当前用户</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<h3>方案3：清除Session重新登录</h3>";
    echo "<p>清除所有Session数据，重新开始</p>";
    echo "<form method='POST' style='display:inline;'>";
    echo "<input type='hidden' name='action' value='clear_session'>";
    echo "<button type='submit' class='btn btn-danger'>🗑️ 清除Session</button>";
    echo "</form>";
    echo "</div>";
    
    echo "<hr>";
    
    echo "<h3>📋 当前状态</h3>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>用户名:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : '未设置') . "</p>";
    echo "<p><strong>权限级别:</strong> " . (isset($_SESSION['ok']) ? $_SESSION['ok'] : '未设置') . "</p>";
    
    echo "<h3>🔗 其他选项</h3>";
    echo "<a href='index.php' class='btn'>🔑 登录页面</a>";
    echo "<a href='fix_permission.php' class='btn'>🔍 详细诊断</a>";
    echo "<a href='debug.php' class='btn'>🛠️ 系统诊断</a>";
}

echo "</div>";
?>
