<?php
// 权限问题诊断和修复工具
session_start();

echo "<h1>🔧 权限问题诊断和修复</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
    .info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .success { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; color: #2e7d32; }
    .error { background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0; color: #c62828; }
    .warning { background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; color: #856404; }
    .btn { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; border: none; cursor: pointer; }
    .btn:hover { background: #005a8b; }
    .btn-success { background: #28a745; }
    .btn-danger { background: #dc3545; }
    .btn-warning { background: #ffc107; color: #212529; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>";

echo "<div class='warning'>";
echo "<strong>⚠️ 检测到权限不足错误</strong><br>";
echo "这通常是因为 Session 中的权限数据有问题。让我们来诊断和修复。";
echo "</div>";

// 显示当前 Session 信息
echo "<h2>📋 当前 Session 状态</h2>";
echo "<div class='info'>";
echo "<strong>Session ID：</strong>" . session_id() . "<br>";
echo "<strong>Session 数据：</strong><br>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";
echo "</div>";

// 检查关键 Session 变量
echo "<h2>🔍 关键变量检查</h2>";
echo "<div class='info'>";

$issues = [];

if (!isset($_SESSION['username'])) {
    $issues[] = "❌ \$_SESSION['username'] 未设置";
} else {
    echo "✅ username: " . htmlspecialchars($_SESSION['username']) . "<br>";
}

if (!isset($_SESSION['ok'])) {
    $issues[] = "❌ \$_SESSION['ok'] 未设置";
} else {
    echo "✅ ok: " . $_SESSION['ok'] . " (" . ($_SESSION['ok'] == 1 ? '管理员' : '普通用户') . ")<br>";
}

if (!isset($_SESSION['user_id'])) {
    $issues[] = "⚠️ \$_SESSION['user_id'] 未设置（可选）";
} else {
    echo "✅ user_id: " . $_SESSION['user_id'] . "<br>";
}

echo "</div>";

// 显示问题
if (!empty($issues)) {
    echo "<div class='error'>";
    echo "<strong>🚨 发现的问题：</strong><br>";
    foreach ($issues as $issue) {
        echo $issue . "<br>";
    }
    echo "</div>";
}

// 数据库验证
echo "<h2>🗄️ 数据库验证</h2>";
try {
    require_once 'config.php';
    $pdo = connectDB();
    
    if (isset($_SESSION['username'])) {
        $stmt = $pdo->prepare("SELECT * FROM tab WHERE username = ?");
        $stmt->execute([$_SESSION['username']]);
        $user_data = $stmt->fetch();
        
        if ($user_data) {
            echo "<div class='success'>";
            echo "<strong>✅ 数据库中的用户信息：</strong><br>";
            echo "ID: " . $user_data['id'] . "<br>";
            echo "用户名: " . htmlspecialchars($user_data['username']) . "<br>";
            echo "权限: " . $user_data['ok'] . " (" . ($user_data['ok'] == 1 ? '管理员' : '普通用户') . ")<br>";
            echo "注册时间: " . $user_data['regtime'] . "<br>";
            echo "</div>";
            
            // 检查 Session 和数据库是否一致
            if (isset($_SESSION['ok']) && $_SESSION['ok'] != $user_data['ok']) {
                echo "<div class='error'>";
                echo "<strong>🚨 数据不一致！</strong><br>";
                echo "Session 中的权限: " . $_SESSION['ok'] . "<br>";
                echo "数据库中的权限: " . $user_data['ok'] . "<br>";
                echo "需要重新登录以同步数据！";
                echo "</div>";
            }
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ 数据库中未找到用户：</strong>" . htmlspecialchars($_SESSION['username']);
            echo "</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ 数据库连接错误：</strong>" . $e->getMessage();
    echo "</div>";
}

// 修复选项
echo "<h2>🔧 修复选项</h2>";

// 选项1：重新登录
echo "<div class='warning'>";
echo "<strong>方案1：重新登录（推荐）</strong><br>";
echo "清除当前 Session 并重新登录，这是最安全的解决方案。<br>";
echo "<a href='close.php' class='btn btn-warning'>🚪 退出并重新登录</a>";
echo "</div>";

// 选项2：修复 Session（如果用户存在且是管理员）
if (isset($user_data) && $user_data && $user_data['ok'] == 1) {
    echo "<div class='info'>";
    echo "<strong>方案2：修复当前 Session</strong><br>";
    echo "您在数据库中确实是管理员，可以尝试修复 Session 数据。<br>";
    
    if ($_POST && isset($_POST['fix_session'])) {
        $_SESSION['username'] = $user_data['username'];
        $_SESSION['ok'] = $user_data['ok'];
        $_SESSION['user_id'] = $user_data['id'];
        $_SESSION['login_time'] = time();
        
        echo "<div class='success'>";
        echo "✅ Session 已修复！请尝试重新访问 user.php<br>";
        echo "<a href='user.php' class='btn btn-success'>🚀 访问用户管理</a>";
        echo "</div>";
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='fix_session' class='btn btn-success'>🔧 修复 Session</button>";
        echo "</form>";
    }
    echo "</div>";
}

// 选项3：使用默认管理员账户
echo "<div class='info'>";
echo "<strong>方案3：使用默认管理员账户</strong><br>";
echo "如果您的账户有问题，可以使用默认管理员账户：<br>";
echo "用户名：admin<br>";
echo "密码：admin123<br>";
echo "<a href='index.php' class='btn'>🔑 前往登录页面</a>";
echo "</div>";

// 调试信息
echo "<h2>🔍 调试信息</h2>";
echo "<div class='info'>";
echo "<strong>check_permission 函数逻辑：</strong><br>";
echo "<pre>";
echo "function check_permission(\$required_level = 1) {
    // 检查是否登录
    if (!isset(\$_SESSION['username']) || !isset(\$_SESSION['ok'])) {
        header('Location: index.php');
        exit();
    }
    
    // 检查权限级别
    if (\$_SESSION['ok'] < \$required_level) {
        die('权限不足！');  // ← 这里触发了错误
    }
    
    return true;
}";
echo "</pre>";

echo "<strong>user.php 要求的权限：</strong>check_permission(1) - 需要管理员权限<br>";
echo "<strong>您当前的权限：</strong>" . (isset($_SESSION['ok']) ? $_SESSION['ok'] : '未设置') . "<br>";
echo "<strong>权限对比：</strong>";
if (isset($_SESSION['ok'])) {
    if ($_SESSION['ok'] >= 1) {
        echo "<span style='color:green;'>✅ 权限足够</span>";
    } else {
        echo "<span style='color:red;'>❌ 权限不足</span>";
    }
} else {
    echo "<span style='color:red;'>❌ 权限未设置</span>";
}
echo "</div>";

// 快速测试
echo "<h2>🧪 快速测试</h2>";
echo "<div class='info'>";
echo "<strong>测试链接：</strong><br>";
echo "<a href='debug.php' class='btn'>🔍 系统诊断</a>";
echo "<a href='test_user_access.php' class='btn'>🔐 权限测试</a>";
echo "<a href='c.php' class='btn'>🏛️ 管理控制台</a>";
echo "<a href='e.php' class='btn'>🏠 用户中心</a>";
echo "</div>";

echo "<hr>";
echo "<h2>💡 总结</h2>";
echo "<div class='warning'>";
echo "<strong>权限不足错误的常见原因：</strong><br>";
echo "1. Session 数据丢失或损坏<br>";
echo "2. 用户权限级别不够（ok < 1）<br>";
echo "3. 数据库和 Session 数据不一致<br>";
echo "4. Session 过期<br><br>";

echo "<strong>推荐解决方案：</strong><br>";
echo "1. 🥇 重新登录（最安全）<br>";
echo "2. 🥈 修复 Session（如果确认是管理员）<br>";
echo "3. 🥉 使用默认管理员账户<br>";
echo "</div>";
?>
