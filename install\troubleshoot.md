# 安装故障排除指南

## 常见安装错误及解决方案

### 1. 数据库连接错误

**错误信息**：`数据库连接失败: Access denied for user 'root'@'localhost'`

**解决方案**：
- 检查MySQL服务是否启动
- 确认数据库用户名和密码正确
- 确认MySQL用户有创建数据库的权限

### 2. 重复键名错误

**错误信息**：`SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'username'`

**解决方案**：
1. 使用数据库重置工具：访问 `install/reset.php`
2. 或手动删除数据库：
   ```sql
   DROP DATABASE IF EXISTS member_system;
   ```
3. 重新运行安装程序

### 3. 权限不足错误

**错误信息**：`Access denied for user 'root'@'localhost' to database 'member_system'`

**解决方案**：
```sql
-- 登录MySQL管理员账户
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 字符集错误

**错误信息**：`Unknown charset: 'utf8mb4'`

**解决方案**：
- 升级MySQL到5.5.3+版本
- 或修改字符集为utf8

### 5. 文件权限错误

**错误信息**：`Permission denied`

**解决方案**：
```bash
# 设置正确的文件权限
chmod 755 install/
chmod 644 *.php
chmod 755 backups/
```

## 手动安装步骤

如果自动安装失败，可以手动执行以下SQL：

```sql
-- 1. 创建数据库
CREATE DATABASE IF NOT EXISTS `member_system` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `member_system`;

-- 2. 创建用户表
CREATE TABLE `tab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `ok` tinyint(1) NOT NULL DEFAULT 2 COMMENT '1=管理员, 2=普通用户',
  `regtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `ok` (`ok`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 创建内容表
CREATE TABLE `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `content` longtext NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'general' COMMENT 'general=通用内容, personal=个人内容',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `username` (`username`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 插入默认管理员
INSERT INTO `tab` (`username`, `password`, `ok`) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);
-- 密码是：admin123
```

## 配置文件设置

手动编辑 `config.php`：

```php
<?php
// 数据库连接配置
$db_host = 'localhost';        // 数据库主机
$db_username = 'root';         // 数据库用户名  
$db_password = '111111';       // 数据库密码
$db_name = 'member_system';    // 数据库名称
?>
```

## 验证安装

安装完成后，访问以下页面验证：

1. **登录页面**：`http://your-domain.com/index.php`
2. **管理后台**：使用 admin/admin123 登录
3. **用户中心**：创建普通用户测试

## 安全建议

安装完成后立即执行：

1. **修改默认密码**
2. **删除install目录**
3. **设置文件权限**：
   ```bash
   chmod 644 *.php
   chmod 755 backups/
   ```
4. **创建install.lock文件**：
   ```bash
   touch install.lock
   ```

## 联系支持

如果问题仍然存在：

1. 检查PHP错误日志
2. 检查MySQL错误日志  
3. 确认PHP版本 >= 7.4
4. 确认MySQL版本 >= 5.7

## 常用MySQL命令

```sql
-- 查看数据库
SHOW DATABASES;

-- 查看表结构
DESCRIBE tab;
DESCRIBE content;

-- 查看用户权限
SHOW GRANTS FOR 'root'@'localhost';

-- 重置root密码（如果忘记）
ALTER USER 'root'@'localhost' IDENTIFIED BY '111111';
```
