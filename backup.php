<?php
// 开启会话
session_start();

// 检查用户是否登录且为管理员
if (!isset($_SESSION['user_id']) || $_SESSION['ok'] != 1) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 处理备份请求
$backup_message = '';

if (isset($_POST['backup'])) {
    // 创建备份目录（如果不存在）
    if (!file_exists('backups')) {
        mkdir('backups', 0777, true);
    }
    
    // 构建备份文件名
    $date = date('Y-m-d_H-i-s');
    $backup_file = "backups/backup_{$date}.sql";
    
    // 获取所有表名
    $tables = array();
    $result = $conn->query('SHOW TABLES');
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }
    
    // 构建SQL内容
    $output = "-- 数据库备份\n-- 创建时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    foreach ($tables as $table) {
        // 表结构
        $result = $conn->query("SHOW CREATE TABLE $table");
        $row = $result->fetch_row();
        $output .= "\n-- 表结构 `$table`\n";
        $output .= $row[1] . ";\n\n";
        
        // 表数据
        $result = $conn->query("SELECT * FROM $table");
        
        if ($result->num_rows > 0) {
            $output .= "-- 表数据 `$table`\n";
            
            while ($row = $result->fetch_assoc()) {
                $values = array();
                foreach ($row as $value) {
                    if (is_null($value)) {
                        $values[] = "NULL";
                    } else {
                        // 使用real_escape_string处理字符串值
                        if (!is_numeric($value)) {
                            $value = '\"' . $conn->real_escape_string($value) . '\"';
                        }
                        $values[] = $value;
                    }
                }
                $output .= "INSERT INTO `$table` (`" . implode('`, `', array_keys($row)) . "`) VALUES (" . implode(', ', $values) . ");\n";
            }
            
            $output .= "\n";
        }
    }
    
    // 保存备份文件
    file_put_contents($backup_file, $output);
    
    if (file_exists($backup_file)) {
        $backup_message = "数据库备份成功创建于：$backup_file";
    } else {
        $backup_message = "数据库备份失败";
    }
}

// 获取现有备份文件列表
$backups = glob('backups/*.sql');
// 按时间倒序排序
usort($backups, function($a, $b) {
    return filemtime($b) - filemtime($a);
});

// 处理文件删除
if (isset($_GET['delete'])) {
    $file_to_delete = urldecode($_GET['delete']);
    if (file_exists($file_to_delete)) {
        if (unlink($file_to_delete)) {
            $backup_message = "备份文件已成功删除";
            // 刷新备份文件列表
            $backups = glob('backups/*.sql');
            usort($backups, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
        } else {
            $backup_message = "备份文件删除失败";
        }
    } else {
        $backup_message = "备份文件不存在";
    }
}

// 处理批量删除
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'delete_selected' && isset($_POST['selected_backups'])) {
    $selected_files = $_POST['selected_backups'];
    $deleted_count = 0;
    
    foreach ($selected_files as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                $deleted_count++;
            }
        }
    }
    
    if ($deleted_count > 0) {
        $backup_message = "成功删除了 $deleted_count 个备份文件";
        // 刷新备份文件列表
        $backups = glob('backups/*.sql');
        usort($backups, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
    } else {
        $backup_message = "没有文件被删除";
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>数据备份</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .form-container {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        .backup-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .card-title {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .card-value {
            font-size: 18px;
            font-weight: bold;
        }
        .success {
            background-color: #4CAF50;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .error {
            background-color: #f44336;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .backups-list {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
        th {
            background-color: rgba(0, 0, 0, 0.3);
        }
        .checkbox-cell {
            text-align: center;
        }
        input[type="checkbox"] {
            transform: scale(1.2);
        }
        .action-btn {
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-right: 5px;
        }
        .action-btn:hover {
            background-color: #f2f2f2;
        }
        .download-btn {
            background-color: #4CAF50;
            color: white;
        }
        .delete-btn {
            background-color: #f44336;
            color: white;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
        .nav-link {
            display: block;
            padding: 10px 0;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .nav-link:hover {
            color: #ffd700;
        }
        .backup-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .backup-table-container {
            overflow-x: auto;
        }
        .select-all-container {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .select-all-container label {
            font-weight: bold;
        }
        .bulk-actions {
            margin-top: 15px;
        }
        .bulk-actions select {
            padding: 8px;
            margin-right: 10px;
            border-radius: 5px;
            border: none;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            font-weight: bold;
        }
        .bulk-actions button {
            padding: 8px 12px;
            border-radius: 5px;
            border: none;
            background-color: #ffffff;
            color: #8e0000;
            font-weight: bold;
            cursor: pointer;
        }
        .bulk-actions button:hover {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据备份</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="#create-backup" class="nav-link">创建备份</a>
            <a href="#manage-backups" class="nav-link">管理备份</a>
            <a href="#restore" class="nav-link" onclick="window.location.href='restore.php'">数据恢复</a>
            <a href="#dashboard" class="nav-link" onclick="window.location.href='c.php'">返回控制台</a>
        </div>
        <div class="main-content">
            <div class="form-container">
                <h2>数据库备份</h2>
                
                <?php if ($backup_message): ?>
                    <?php if (strpos($backup_message, '成功') !== false): ?>
                        <div class="success"><?php echo $backup_message; ?></div>
                    <?php else: ?>
                        <div class="error"><?php echo $backup_message; ?></div>
                    <?php endif; ?>
                <?php endif; ?>
                
                <form method="post">
                    <input type="submit" name="backup" value="立即备份" class="action-btn">
                </form>
                
                <div class="backup-stats">
                    <div class="stat-card">
                        <div class="card-title">备份文件数量</div>
                        <div class="card-value"><?php echo count($backups); ?></div>
                    </div>
                    <div class="stat-card">
                        <div class="card-title">最近备份时间</div>
                        <div class="card-value">
                            <?php 
                                if (!empty($backups)) {
                                    echo date('Y-m-d H:i:s', filemtime($backups[0]));
                                } else {
                                    echo "无备份";
                                }
                            ?>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="card-title">总备份大小</div>
                        <div class="card-value">
                            <?php 
                                $total_size = 0;
                                foreach ($backups as $file) {
                                    $total_size += filesize($file);
                                }
                                echo format_size($total_size);
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="backups-list">
                <div class="backup-actions">
                    <h3>备份文件管理</h3>
                    <form method="post">
                        <input type="hidden" name="action" value="delete_selected">
                        <div class="bulk-actions">
                            <select name="action_type">
                                <option value="delete">删除选中</option>
                            </select>
                            <button type="submit">执行操作</button>
                        </div>
                </div>
                
                <div class="select-all-container">
                    <label><input type="checkbox" id="select-all"> 全选</label>
                    <span>共 <?php echo count($backups); ?> 个备份文件</span>
                </div>
                
                <div class="backup-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th class="checkbox-cell"><input type="checkbox" id="select-all"></th>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($backups)): ?>
                                <?php foreach ($backups as $file): ?>
                                <tr>
                                    <td class="checkbox-cell">
                                        <input type="checkbox" name="selected_backups[]" value="<?php echo $file; ?>">
                                    </td>
                                    <td><?php echo basename($file); ?></td>
                                    <td><?php echo format_size(filesize($file)); ?></td>
                                    <td><?php echo date('Y-m-d H:i:s', filemtime($file)); ?></td>
                                    <td>
                                        <a href="<?php echo $file; ?>" class="action-btn download-btn">下载</a>
                                        <a href="backup.php?delete=<?php echo urlencode($file); ?>" class="action-btn delete-btn" onclick="return confirm('确定要删除这个备份吗？')">删除</a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" style="text-align: center;">没有找到备份文件</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <script>
                    document.getElementById('select-all').addEventListener('change', function() {
                        var checkboxes = document.querySelectorAll('input[name="selected_backups[]"]');
                        for (var checkbox of checkboxes) {
                            checkbox.checked = this.checked;
                        }
                    });
                </script>
                
                </form>
            </div>
        </div>
    </div>
</body>
</html>
<?php
// 文件大小格式化函数
function format_size($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } elseif ($bytes > 1) {
        return $bytes . ' bytes';
    } elseif ($bytes == 1) {
        return '1 byte';
    } else {
        return '0 bytes';
    }
}
?>