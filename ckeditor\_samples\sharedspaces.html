<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Shared Toolbars &mdash; CKEditor Sample</title>
	<meta content="text/html; charset=utf-8" http-equiv="content-type" />
	<script type="text/javascript" src="../ckeditor.js"></script>
	<script src="sample.js" type="text/javascript"></script>
	<link href="sample.css" rel="stylesheet" type="text/css" />
	<style id="styles" type="text/css">

		#editorsForm
		{
			height: 400px;
			overflow: auto;
			border: solid 1px #555;
			margin: 10px 0;
			padding: 0 10px;
		}

	</style>
</head>
<body>
	<h1 class="samples">
		CKEditor Sample &mdash; Shared Toolbars
	</h1>
	<div class="description">
	<p>
		This sample shows how to configure multiple CKEditor instances to share some parts of the interface.
		You can choose to share the toolbar (<code>topSpace</code>), the elements path
		(<code>bottomSpace</code>), or both.
	</p>
	<p>
		CKEditor instances with shared spaces can be inserted with a JavaScript call using the following code:
	</p>
	<pre class="samples">CKEDITOR.replace( '<em>textarea_id</em>',
	{
		<strong>sharedSpaces :
		{
			top : 'topSpace',
			bottom : 'bottomSpace'
		}</strong>
	});</pre>
	<p>
		Note that <code><em>textarea_id</em></code> in the code above is the <code>id</code> attribute of
		the <code>&lt;textarea&gt;</code> element to be replaced with CKEditor.
	</p>
	</div>
	<!-- This <div> holds alert messages to be display in the sample page. -->
	<div id="alerts">
		<noscript>
			<p>
				<strong>CKEditor requires JavaScript to run</strong>. In a browser with no JavaScript
				support, like yours, you should still see the contents (HTML data) and you should
				be able to edit it normally, without a rich editor interface.
			</p>
		</noscript>
	</div>
	<div id="topSpace">
	</div>
	<form action="sample_posteddata.php" id="editorsForm" method="post">
		<p>
			<label for="editor1">
				Editor 1 (uses the shared toolbar and elements path):</label>
			<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor2">
				Editor 2 (uses the shared toolbar and elements path):</label>
			<textarea cols="80" id="editor2" name="editor2" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor3">
				Editor 3 (uses the shared toolbar only):</label>
			<textarea cols="80" id="editor3" name="editor3" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor4">
				Editor 4 (no shared spaces):</label>
			<textarea cols="80" id="editor4" name="editor4" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		</p>
		<p>
			<input type="submit" value="Submit" />
		</p>
	</form>
	<div id="bottomSpace">
	</div>
	<div id="footer">
		<hr />
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2011, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
	<script type="text/javascript">
	//<![CDATA[

		// Create all editor instances at the end of the page, so we are sure
		// that the "bottomSpace" div is available in the DOM (IE issue).

		CKEDITOR.replace( 'editor1',
			{
				sharedSpaces :
				{
					top : 'topSpace',
					bottom : 'bottomSpace'
				},

				// Removes the maximize plugin as it's not usable
				// in a shared toolbar.
				// Removes the resizer as it's not usable in a
				// shared elements path.
				removePlugins : 'maximize,resize'
			} );

		CKEDITOR.replace( 'editor2',
			{
				sharedSpaces :
				{
					top : 'topSpace',
					bottom : 'bottomSpace'
				},

				// Removes the maximize plugin as it's not usable
				// in a shared toolbar.
				// Removes the resizer as it's not usable in a
				// shared elements path.
				removePlugins : 'maximize,resize'
			} );

		CKEDITOR.replace( 'editor3',
			{
				sharedSpaces :
				{
					top : 'topSpace'
				},

				// Removes the maximize plugin as it's not usable
				// in a shared toolbar.
				removePlugins : 'maximize'
			} );

		CKEDITOR.replace( 'editor4' );
	//]]>
	</script>
</body>
</html>
