<?php
/**
 * 会员管理系统 - 安装程序
 * 深红色主题版本
 */

// 检查是否已经安装过
if (file_exists('../config.php')) {
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>系统已安装</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                color: white;
            }
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
            .message-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 50px 40px;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
                text-align: center;
                color: #8B0000;
            }
            h1 { margin-bottom: 20px; }
            .btn {
                display: inline-block;
                padding: 15px 30px;
                background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
                color: white;
                text-decoration: none;
                border-radius: 15px;
                margin: 10px;
                transition: all 0.3s ease;
            }
            .btn:hover {
                transform: translateY(-3px);
                box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
            }
        </style>
    </head>
    <body>
        <div class="message-container">
            <h1>系统已安装</h1>
            <p>会员管理系统已经安装完成，如需重新安装请先删除config.php文件。</p>
            <a href="../index.php" class="btn">返回首页</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

$error = '';
$success = '';

// 检查是否提交了数据库信息
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $host = trim($_POST['host'] ?? '');
    $user = trim($_POST['user'] ?? '');
    $pass = $_POST['pass'] ?? '';
    $dbname = trim($_POST['dbname'] ?? '');

    // 验证输入
    if (empty($host) || empty($user) || empty($dbname)) {
        $error = '请填写完整的数据库信息！';
    } else {
        try {
            // 创建数据库连接
            $conn = new mysqli($host, $user, $pass);

            // 检查数据库连接
            if ($conn->connect_error) {
                throw new Exception("数据库连接失败: " . $conn->connect_error);
            }

            // 创建数据库
            $sql = "CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if (!$conn->query($sql)) {
                throw new Exception("数据库创建失败: " . $conn->error);
            }

            // 使用数据库
            if (!$conn->select_db($dbname)) {
                throw new Exception("选择数据库失败: " . $conn->error);
            }

            // 读取SQL文件
            $sqlFile = file_get_contents('database.sql');
            if ($sqlFile === false) {
                throw new Exception("无法读取数据库结构文件");
            }

            // 分割SQL语句并执行
            $sqlStatements = explode(';', $sqlFile);
            foreach ($sqlStatements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^\/\//', $statement)) {
                    if (!$conn->query($statement)) {
                        throw new Exception("执行SQL语句失败: " . $conn->error);
                    }
                }
            }

            // 关闭连接
            $conn->close();

            // 写入配置文件
            $configContent = "<?php
/**
 * 会员管理系统 - 系统配置文件
 * 深红色主题版本 - 自动生成
 */

// 防止直接访问
if (!defined('SYSTEM_ACCESS')) {
    define('SYSTEM_ACCESS', true);
}

// 系统基本信息
define('SYSTEM_NAME', '会员管理系统');
define('VERSION', '2.0');
define('AUTHOR', 'Deep Red Theme');

// 数据库配置
\$host = '$host';        // 数据库服务器
\$user = '$user';        // 数据库用户名
\$pass = '$pass';        // 数据库密码
\$dbname = '$dbname';    // 数据库名

// 系统配置
define('TIMEZONE', 'Asia/Shanghai');
define('CHARSET', 'utf8mb4');
define('DEBUG_MODE', false);

// 设置时区
date_default_timezone_set(TIMEZONE);

// 错误报告设置
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * 创建数据库连接 - PDO方式
 */
function connectDB() {
    global \$host, \$user, \$pass, \$dbname;

    try {
        \$dsn = \"mysql:host=\$host;dbname=\$dbname;charset=\" . CHARSET;
        \$options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES \" . CHARSET
        ];

        \$pdo = new PDO(\$dsn, \$user, \$pass, \$options);
        return \$pdo;
    } catch (PDOException \$e) {
        error_log(\"数据库连接失败: \" . \$e->getMessage());
        die(\"数据库连接失败，请检查配置！\");
    }
}

/**
 * 创建数据库连接 - MySQLi方式（兼容旧代码）
 */
function connectMySQLi() {
    global \$host, \$user, \$pass, \$dbname;

    \$conn = new mysqli(\$host, \$user, \$pass, \$dbname);

    if (\$conn->connect_error) {
        error_log(\"MySQLi连接失败: \" . \$conn->connect_error);
        die(\"数据库连接失败: \" . \$conn->connect_error);
    }

    \$conn->set_charset(CHARSET);
    return \$conn;
}

/**
 * 安全的数据库查询
 */
function safe_query(\$pdo, \$sql, \$params = []) {
    try {
        \$stmt = \$pdo->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    } catch (PDOException \$e) {
        error_log(\"SQL查询错误: \" . \$e->getMessage() . \" SQL: \" . \$sql);
        throw new Exception(\"数据库查询失败\");
    }
}

/**
 * 安全输出HTML内容
 */
function safe_output(\$content) {
    return htmlspecialchars(\$content, ENT_QUOTES, 'UTF-8');
}

/**
 * 检查用户登录状态
 */
function check_login() {
    if (!isset(\$_SESSION['user_id']) || !isset(\$_SESSION['user']) || !isset(\$_SESSION['ok'])) {
        header('Location: index.php');
        exit();
    }
}

/**
 * 检查管理员权限
 */
function check_admin() {
    check_login();
    if (\$_SESSION['ok'] != 1) {
        header('Location: e.php');
        exit();
    }
}

/**
 * 获取用户信息
 */
function get_user_info(\$user_id) {
    try {
        \$pdo = connectDB();
        \$stmt = safe_query(\$pdo, \"SELECT * FROM tab WHERE id = ?\", [\$user_id]);
        return \$stmt->fetch();
    } catch (Exception \$e) {
        error_log(\"获取用户信息失败: \" . \$e->getMessage());
        return false;
    }
}

/**
 * 获取系统统计信息
 */
function get_system_stats() {
    try {
        \$pdo = connectDB();

        // 总用户数
        \$stmt = safe_query(\$pdo, \"SELECT COUNT(*) as total FROM tab\");
        \$total_users = \$stmt->fetch()['total'];

        // 管理员数
        \$stmt = safe_query(\$pdo, \"SELECT COUNT(*) as total FROM tab WHERE ok = 1\");
        \$admin_users = \$stmt->fetch()['total'];

        // 普通用户数
        \$stmt = safe_query(\$pdo, \"SELECT COUNT(*) as total FROM tab WHERE ok = 2\");
        \$normal_users = \$stmt->fetch()['total'];

        // 内容数
        \$stmt = safe_query(\$pdo, \"SELECT COUNT(*) as total FROM content\");
        \$total_content = \$stmt->fetch()['total'];

        return [
            'total_users' => \$total_users,
            'admin_users' => \$admin_users,
            'normal_users' => \$normal_users,
            'total_content' => \$total_content
        ];
    } catch (Exception \$e) {
        error_log(\"获取系统统计失败: \" . \$e->getMessage());
        return [
            'total_users' => 0,
            'admin_users' => 0,
            'normal_users' => 0,
            'total_content' => 0
        ];
    }
}

/**
 * 记录系统日志
 */
function log_action(\$action, \$details = '') {
    \$log_entry = date('Y-m-d H:i:s') . \" - \" . \$action;
    if (\$details) {
        \$log_entry .= \" - \" . \$details;
    }
    if (isset(\$_SESSION['user'])) {
        \$log_entry .= \" - 用户: \" . \$_SESSION['user'];
    }
    \$log_entry .= \"\\n\";

    error_log(\$log_entry, 3, 'system.log');
}

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

?>";

            if (file_put_contents('../config.php', $configContent) === false) {
                throw new Exception("无法写入配置文件");
            }

            // 锁定安装程序
            file_put_contents('../install.lock', 'Installed at ' . date('Y-m-d H:i:s'));

            $success = "系统安装成功！正在跳转到登录页面...";

            // 3秒后跳转
            echo "<script>setTimeout(function(){ window.location.href = '../index.php'; }, 3000);</script>";

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>会员管理系统安装</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .install-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px 40px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            max-width: 500px;
            width: 90%;
            position: relative;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .install-container:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }

        .install-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .install-header h2 {
            color: #8B0000;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 700;
        }

        .install-header p {
            color: #666;
            font-size: 1.1em;
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        .form-group input:focus {
            outline: none;
            border-color: #8B0000;
            box-shadow: 0 0 20px rgba(139, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
            transform: scale(1.02);
        }

        .form-group input::placeholder {
            color: #999;
        }

        .btn-install {
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
            color: white;
            padding: 18px 30px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .btn-install::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-install:hover::before {
            left: 100%;
        }

        .btn-install:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }

        .btn-install:active {
            transform: translateY(-1px);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }

        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .system-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(139, 0, 0, 0.2);
        }

        .system-info p {
            color: #666;
            font-size: 12px;
            margin: 5px 0;
        }

        @media (max-width: 480px) {
            .install-container {
                padding: 30px 25px;
                margin: 20px;
            }

            .install-header h2 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h2>系统安装</h2>
            <p>会员管理系统 - 深红色主题版</p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                ⚠️ <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                ✅ <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <?php if (!$success): ?>
        <form method="post">
            <div class="form-group">
                <label for="host">数据库主机</label>
                <input type="text" id="host" name="host"
                       value="<?php echo htmlspecialchars($_POST['host'] ?? 'localhost'); ?>"
                       placeholder="localhost" required>
            </div>

            <div class="form-group">
                <label for="user">数据库用户名</label>
                <input type="text" id="user" name="user"
                       value="<?php echo htmlspecialchars($_POST['user'] ?? 'root'); ?>"
                       placeholder="root" required>
            </div>

            <div class="form-group">
                <label for="pass">数据库密码</label>
                <input type="password" id="pass" name="pass"
                       value="<?php echo htmlspecialchars($_POST['pass'] ?? '111111'); ?>"
                       placeholder="请输入数据库密码">
            </div>

            <div class="form-group">
                <label for="dbname">数据库名称</label>
                <input type="text" id="dbname" name="dbname"
                       value="<?php echo htmlspecialchars($_POST['dbname'] ?? 'member_system'); ?>"
                       placeholder="member_system" required>
            </div>

            <button type="submit" class="btn-install">开始安装</button>
        </form>
        <?php endif; ?>

        <div class="system-info">
            <p>会员管理系统 v2.0 - 深红色主题版</p>
            <p>安装时间：<?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>

    <script>
        // 表单验证
        document.querySelector('form')?.addEventListener('submit', function(e) {
            const host = document.getElementById('host')?.value.trim();
            const user = document.getElementById('user')?.value.trim();
            const dbname = document.getElementById('dbname')?.value.trim();

            if (!host || !user || !dbname) {
                e.preventDefault();
                alert('请填写完整的数据库信息！');
                return false;
            }
        });

        // 自动聚焦到第一个输入框
        document.getElementById('host')?.focus();
    </script>
</body>
</html>