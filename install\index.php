<?php
// 检查是否已经安装过
if (file_exists('../../config.php')) {
    echo "系统已安装，如需重新安装请先删除config.php文件";
    exit;
}

// 检查是否提交了数据库信息
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $host = $_POST['host'];
    $user = $_POST['user'];
    $pass = $_POST['pass'];
    $dbname = $_POST['dbname'];

    // 创建数据库连接
    $conn = new mysqli($host, $user, $pass);

    // 检查数据库连接
    if ($conn->connect_error) {
        die("数据库连接失败: " . $conn->connect_error);
    }

    // 创建数据库
    $sql = "CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if ($conn->query($sql) === TRUE) {
        echo "数据库创建成功<br>";
    } else {
        echo "数据库创建失败: " . $conn->error;
        exit;
    }

    // 使用数据库
    $conn->select_db($dbname);

    // 读取SQL文件
    $sqlFile = file_get_contents('../database.sql');
    
    // 执行SQL语句
    if ($conn->multi_query($sqlFile) === TRUE) {
        echo "数据表创建成功<br>";
    } else {
        echo "数据表创建失败: " . $conn->error;
        exit;
    }

    // 关闭连接
    $conn->close();

    // 写入配置文件
    $configContent = "<?php\n// 数据库配置\n$host = '$host'; // 数据库服务器\n$user = '$user'; // 数据库用户名\n$pass = '$pass'; // 数据库密码\n$dbname = '$dbname'; // 数据库名\n\n// 创建数据库连接\n$conn = new mysqli($host, $user, $pass, $dbname);\n\n// 检查数据库连接\nif ($conn->connect_error) {\n    die(\"数据库连接失败: \" . $conn->connect_error);\n}\n\n// 设置字符集\n$conn->set_charset(\"utf8mb4\");\n?>";

    file_put_contents('../../config.php', $configContent);

    // 锁定安装程序
    file_put_contents('../../install.lock', 'Installed');

    echo "系统安装成功！正在跳转...";
    header("Refresh:3; url=../..");
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>会员管理系统安装</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .install-container {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            width: 300px;
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 5px;
            color: white;
        }
        input[type="submit"] {
            width: 100%;
            padding: 10px;
            margin-top: 15px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        input[type="submit"]:hover {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <h2>会员管理系统安装</h2>
        <form method="post">
            <label for="host">数据库主机:</label>
            <input type="text" id="host" name="host" value="localhost" required>
            
            <label for="user">数据库用户名:</label>
            <input type="text" id="user" name="user" value="root" required>
            
            <label for="pass">数据库密码:</label>
            <input type="password" id="pass" name="pass" value="111111" required>
            
            <label for="dbname">数据库名称:</label>
            <input type="text" id="dbname" name="dbname" value="member_system" required>
            
            <input type="submit" value="安装系统">
        </form>
    </div>
</body>
</html>