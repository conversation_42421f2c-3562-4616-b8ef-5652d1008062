/*
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

.cke_skin_office2003 .cke_contextmenu
{
	padding: 2px;
}

.cke_skin_office2003 .cke_menuitem a
{
	display:block;
}

.cke_skin_office2003 .cke_menuitem span
{
	cursor: default;
}

.cke_skin_office2003 .cke_menuitem a:hover,
.cke_skin_office2003 .cke_menuitem a:focus,
.cke_skin_office2003 .cke_menuitem a:active
{
	background-color: #8db1ff;
	display:block;
}

.cke_hc .cke_menuitem a:hover,
.cke_hc .cke_menuitem a:focus,
.cke_hc .cke_menuitem a:active
{
	border: 2px solid;
}

.cke_skin_office2003 .cke_menuitem .cke_icon
{
	background-image: url(icons.png);
	background-position: 100px;
	background-repeat:no-repeat;
	background-color: transparent;
	width: 16px;
	height: 16px;
	float: left;
}

.cke_rtl .cke_skin_office2003 .cke_menuitem .cke_icon
{
	background-image: url(icons_rtl.png);
}

/* IE with zoom != 100% will distort the icons otherwise #4821 */
.cke_browser_ie .cke_skin_office2003 .cke_menuitem .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_browser_ie.cke_rtl .cke_skin_office2003 .cke_menuitem .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_browser_ie6 .cke_skin_office2003 .cke_menuitem .cke_icon,
.cke_browser_ie .cke_skin_office2003 .cke_menuitem.cke_noalphafix .cke_icon
{
	filter: ;
}

.cke_skin_office2003 .cke_menuitem .cke_disabled .cke_icon
{
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
}

/* IE with zoom != 100% will distort the icons otherwise #4821 */
.cke_browser_ie .cke_skin_office2003 .cke_menuitem .cke_disabled .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_browser_ie.cke_rtl .cke_skin_office2003 .cke_menuitem .cke_disabled .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_browser_ie6 .cke_skin_office2003 .cke_menuitem .cke_disabled .cke_icon,
.cke_browser_ie .cke_skin_office2003 .cke_menuitem.cke_noalphafix .cke_disabled .cke_icon
{
	filter: ;
}

.cke_skin_office2003 .cke_menuitem .cke_icon_wrapper
{
	background-color: #f7f8fd;
	border: solid 4px #f7f8fd;
	width: 16px;
	height: 16px;
	float: left;
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
	clear: both;
}

.cke_rtl .cke_skin_office2003 .cke_menuitem .cke_icon_wrapper
{
	float: right;
}

.cke_skin_office2003 .cke_menuitem a:hover .cke_icon_wrapper,
.cke_skin_office2003 .cke_menuitem a:focus .cke_icon_wrapper,
.cke_skin_office2003 .cke_menuitem a:active .cke_icon_wrapper
{
	background-color: #9d9d9d;
	border: solid 4px #9d9d9d;
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
}

.cke_skin_office2003 .cke_menuitem a:hover.cke_disabled .cke_icon_wrapper,
.cke_skin_office2003 .cke_menuitem a:focus.cke_disabled .cke_icon_wrapper,
.cke_skin_office2003 .cke_menuitem a:active.cke_disabled .cke_icon_wrapper
{
	background-color: #f7f8fd;
	border: solid 4px #f7f8fd;
}

.cke_skin_office2003 .cke_menuitem .cke_label
{
	display:block;
	padding-right: 3px;
	padding-top: 5px;
	padding-left: 4px;
	height:19px;
	margin-left: 24px;
	background-color: #fff;
}
/* Set these after the document has been loaded and we know the dimensions*/
.cke_skin_office2003 .cke_frameLoaded .cke_menuitem .cke_label
{
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
}

.cke_rtl .cke_skin_office2003 .cke_menuitem .cke_label
{
	padding-right: 0;
	margin-left: 0;
	padding-left: 3px;
	margin-right: 28px;
}

.cke_skin_office2003 .cke_menuitem a.cke_disabled .cke_label
{
	filter: alpha(opacity=30); /* IE */
	opacity: 0.30; /* Safari, Opera and Mozilla */
}

.cke_skin_office2003 .cke_menuitem a:hover .cke_label,
.cke_skin_office2003 .cke_menuitem a:focus .cke_label,
.cke_skin_office2003 .cke_menuitem a:active .cke_label
{
	background-color: #8db1ff;
}

.cke_skin_office2003 .cke_menuitem a.cke_disabled:hover .cke_label,
.cke_skin_office2003 .cke_menuitem a.cke_disabled:focus .cke_label,
.cke_skin_office2003 .cke_menuitem a.cke_disabled:active .cke_label
{
	background-color: transparent;
}

.cke_skin_office2003 .cke_menuseparator
{
	background-color: #f7f8fd;
	height: 2px;
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */

	_font-size: 0;
}

.cke_skin_office2003 .cke_menuarrow
{
	/* arrowright.gif*/
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-position: 0 -1071px;
	background-repeat: no-repeat;
	height: 5px;
	width: 3px;
	float: right;
	margin-right: 2px;
	margin-top: 3px;
}

.cke_rtl .cke_skin_office2003 .cke_menuarrow
{
	float: left;
	margin-right: 0;
	margin-left: 2px;
	/* arrowleft.gif*/
	background-position: 0 -1050px;
}

.cke_skin_office2003 .cke_menuarrow span
{
	display: none;
}

.cke_hc .cke_skin_office2003 .cke_menuarrow
{
	width: auto;
	margin-top: 0;
}

.cke_hc .cke_skin_office2003 .cke_menuarrow span
{
	display: inline;
}

/* #3766 In the context menu, long labels with second level menu get wrapped */
.cke_browser_ie.cke_ltr .cke_skin_office2003 .cke_menuarrow
{
	position: absolute;
	right: 2px;
}

.cke_browser_ie.cke_rtl .cke_skin_office2003 .cke_menuarrow
{
	position: absolute;
	left: 2px;
}
/* END #3766 */
