<?php
/**
 * 会员管理系统 - 普通用户主页
 * 深红色主题版本
 */

// 包含系统配置
require_once 'config.php';

// 检查用户登录状态
check_login();

// 获取当前用户信息
$current_user = get_user_info($_SESSION['user_id']);

if (!$current_user) {
    header('Location: index.php');
    exit();
}

// 获取用户的个人内容
try {
    $pdo = connectDB();

    // 获取用户的个人内容
    $stmt = safe_query($pdo,
        "SELECT * FROM content WHERE user_id = ? ORDER BY time DESC",
        [$_SESSION['user_id']]
    );
    $personal_content = $stmt->fetchAll();

    // 获取通用内容（所有用户可见）
    $stmt = safe_query($pdo,
        "SELECT c.*, t.username FROM content c
         LEFT JOIN tab t ON c.user_id = t.id
         WHERE c.type = 'general'
         ORDER BY c.time DESC LIMIT 10"
    );
    $general_content = $stmt->fetchAll();

    // 获取系统统计信息
    $stats = get_system_stats();

} catch (Exception $e) {
    $personal_content = [];
    $general_content = [];
    $stats = [];
    error_log("获取用户内容错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 用户主页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-welcome {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            border: none;
            padding: 12px 25px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .welcome-section {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .welcome-title {
            font-size: 2.5em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .welcome-subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .user-badge {
            display: inline-block;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1em;
            margin: 10px;
        }

        .badge-admin {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
        }

        .badge-user {
            background: linear-gradient(135deg, #87CEEB, #4682B4);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .stat-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 1em;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 1.8em;
            margin-bottom: 25px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content-grid {
            display: grid;
            gap: 20px;
        }

        .content-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .content-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .content-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .content-meta {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .content-preview {
            line-height: 1.6;
            opacity: 0.9;
        }

        .no-content {
            text-align: center;
            padding: 40px;
            opacity: 0.7;
        }

        .no-content h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .welcome-section {
                padding: 25px;
            }

            .welcome-title {
                font-size: 2em;
            }
        }
    </style>
</head>
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏠 用户主页</h1>
            <div class="user-info">
                <div class="user-welcome">
                    欢迎回来，<?php echo safe_output($current_user['username']); ?>
                </div>
                <a href="close.php" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <div class="welcome-title">
                🎉 欢迎使用会员管理系统
            </div>
            <div class="welcome-subtitle">
                您好，<?php echo safe_output($current_user['username']); ?>！
            </div>
            <div>
                <span class="user-badge <?php echo $current_user['ok'] == 1 ? 'badge-admin' : 'badge-user'; ?>">
                    <?php echo $current_user['ok'] == 1 ? '👑 VIP管理员' : '👤 尊贵会员'; ?>
                </span>
                <span class="user-badge <?php echo $current_user['status'] == 1 ? 'badge-user' : 'badge-admin'; ?>">
                    <?php echo $current_user['status'] == 1 ? '✅ 账户正常' : '🚫 账户禁用'; ?>
                </span>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <div class="stat-value"><?php echo date('Y-m-d', strtotime($current_user['time'])); ?></div>
                <div class="stat-label">加入日期</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📝</div>
                <div class="stat-value"><?php echo count($personal_content); ?></div>
                <div class="stat-label">我的内容</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-value"><?php echo $stats['total_users'] ?? 0; ?></div>
                <div class="stat-label">系统用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🕒</div>
                <div class="stat-value"><?php echo $current_user['last_login'] ? date('m-d H:i', strtotime($current_user['last_login'])) : '首次'; ?></div>
                <div class="stat-label">最后登录</div>
            </div>
        </div>

        <!-- 我的个人内容 -->
        <div class="content-section">
            <h2 class="section-title">📝 我的个人内容</h2>
            <?php if (!empty($personal_content)): ?>
                <div class="content-grid">
                    <?php foreach ($personal_content as $content): ?>
                    <div class="content-item">
                        <div class="content-title">
                            <?php echo safe_output($content['title']); ?>
                        </div>
                        <div class="content-meta">
                            创建时间：<?php echo $content['time']; ?> |
                            类型：<?php echo $content['type'] == 'general' ? '通用内容' : '个人内容'; ?>
                        </div>
                        <div class="content-preview">
                            <?php
                            $preview = strip_tags($content['content']);
                            echo safe_output(mb_substr($preview, 0, 150) . (mb_strlen($preview) > 150 ? '...' : ''));
                            ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-content">
                    <h3>📝 还没有个人内容</h3>
                    <p>您还没有创建任何个人内容，<a href="u.php" style="color: #FFD700; text-decoration: underline;">点击这里</a>开始创建吧！</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- 系统通用内容 -->
        <?php if (!empty($general_content)): ?>
        <div class="content-section">
            <h2 class="section-title">📰 系统公告</h2>
            <div class="content-grid">
                <?php foreach ($general_content as $content): ?>
                <div class="content-item">
                    <div class="content-title">
                        <?php echo safe_output($content['title']); ?>
                    </div>
                    <div class="content-meta">
                        发布者：<?php echo safe_output($content['username']); ?> |
                        发布时间：<?php echo $content['time']; ?>
                    </div>
                    <div class="content-preview">
                        <?php
                        $preview = strip_tags($content['content']);
                        echo safe_output(mb_substr($preview, 0, 200) . (mb_strlen($preview) > 200 ? '...' : ''));
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- 快速操作 -->
        <div class="stats-grid">
            <div class="stat-card" onclick="window.location.href='u.php'" style="cursor: pointer;">
                <div class="stat-icon">✏️</div>
                <div class="stat-value">编辑</div>
                <div class="stat-label">编辑内容</div>
            </div>
            <div class="stat-card" onclick="window.location.href='check.php?id=<?php echo $_SESSION['user_id']; ?>'" style="cursor: pointer;">
                <div class="stat-icon">👁️</div>
                <div class="stat-value">查看</div>
                <div class="stat-label">查看内容</div>
            </div>
            <div class="stat-card" onclick="window.print()" style="cursor: pointer;">
                <div class="stat-icon">🖨️</div>
                <div class="stat-value">打印</div>
                <div class="stat-label">打印页面</div>
            </div>
            <?php if ($current_user['ok'] == 1): ?>
            <div class="stat-card" onclick="window.location.href='c.php'" style="cursor: pointer;">
                <div class="stat-icon">🏛️</div>
                <div class="stat-value">管理</div>
                <div class="stat-label">管理控制台</div>
            </div>
            <?php else: ?>
            <div class="stat-card" onclick="copyPageContent()" style="cursor: pointer;">
                <div class="stat-icon">📋</div>
                <div class="stat-value">复制</div>
                <div class="stat-label">复制内容</div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // 复制页面内容
        function copyPageContent() {
            const contentElements = document.querySelectorAll('.content-preview');
            if (contentElements.length > 0) {
                let allContent = '';
                contentElements.forEach(element => {
                    allContent += element.textContent + '\n\n';
                });

                navigator.clipboard.writeText(allContent).then(() => {
                    alert('✅ 内容已成功复制到剪贴板！');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = allContent;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        alert('✅ 内容已成功复制到剪贴板！');
                    } catch (err) {
                        alert('❌ 复制失败，请手动复制内容。');
                    }
                    document.body.removeChild(textArea);
                });
            } else {
                alert('📝 没有可复制的内容。');
            }
        }

        // 添加卡片点击效果
        document.querySelectorAll('.stat-card[onclick]').forEach(card => {
            card.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(-3px) scale(0.98)';
            });

            card.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-5px) scale(1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 欢迎动画
        window.addEventListener('load', function() {
            const welcomeSection = document.querySelector('.welcome-section');
            welcomeSection.style.opacity = '0';
            welcomeSection.style.transform = 'translateY(30px)';

            setTimeout(() => {
                welcomeSection.style.transition = 'all 0.8s ease';
                welcomeSection.style.opacity = '1';
                welcomeSection.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
