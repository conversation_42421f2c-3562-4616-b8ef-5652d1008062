/*
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

/**
 * Reset and Default Values
 */
.cke_skin_kama *,
.cke_skin_kama a:hover,
.cke_skin_kama a:link,
.cke_skin_kama a:visited,
.cke_skin_kama a:active
{
	margin: 0;
	padding: 0;
	border: 0;
	background: transparent;
	text-decoration: none;
	font: normal normal normal 100% Sans-Serif;
	width: auto;
	height: auto;
	border-collapse: collapse;
	text-align: left;
	vertical-align: baseline;
	white-space: nowrap;
	cursor: auto;
	color: #000;
	float: none;

    font-size: 12px;
    font-family: Arial,Helvetica,Tahoma,Verdana,Sans-Serif;
}

.cke_skin_kama .cke_rtl *,
.cke_skin_kama .cke_rtl a:hover,
.cke_skin_kama .cke_rtl a:link,
.cke_skin_kama .cke_rtl a:visited,
.cke_skin_kama .cke_rtl a:active,
.cke_rtl .cke_skin_kama *,
.cke_rtl .cke_skin_kama a:hover,
.cke_rtl .cke_skin_kama a:link,
.cke_rtl .cke_skin_kama a:visited,
.cke_rtl .cke_skin_kama a:active
{
	text-align: right;
}

.cke_skin_kama iframe
{
	vertical-align: inherit;	/** For IE */
}

.cke_skin_kama textarea
{
	white-space: pre;
}

.cke_skin_kama .cke_browser_gecko textarea
{
	cursor: text;
}

.cke_skin_kama .cke_browser_gecko textarea[disabled]
{
	cursor: default;
}

.cke_skin_kama input[type="text"],
.cke_skin_kama input[type="password"]
{
	cursor: text;
}

.cke_skin_kama input[type="text"][disabled],
.cke_skin_kama input[type="password"][disabled]
{
	cursor: default;
}

.cke_skin_kama fieldset
{
	padding: 10px;
	border: 2px groove #E0DFE3;
}
