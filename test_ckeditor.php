<?php
// CKEditor 功能测试页面
session_start();
require_once 'config.php';

// 简单权限检查
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit();
}

$message = '';
if ($_POST && isset($_POST['test_content'])) {
    $content = $_POST['test_content'] ?? '';
    $message = "✅ 内容接收成功！内容长度：" . mb_strlen($content) . " 字符";
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .editor-container {
            margin-bottom: 30px;
        }
        
        .editor-container label {
            display: block;
            margin-bottom: 15px;
            color: #333;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .ckeditor-textarea {
            width: 100%;
            min-height: 400px;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            font-size: 14px;
            resize: vertical;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .features {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-top: 30px;
        }
        
        .features h3 {
            color: #8B0000;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .features li:last-child {
            border-bottom: none;
        }
        
        .quick-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .quick-links a {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            margin: 0 10px;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .quick-links a:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 CKEditor 功能测试</h1>
            <p>测试富文本编辑器的完整功能</p>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-success">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="" id="testForm">
            <div class="editor-container">
                <label for="test_editor">📝 富文本编辑器测试</label>
                <textarea name="test_content" id="test_editor" class="ckeditor-textarea" 
                          placeholder="在此测试 CKEditor 的各种功能...">
<h2>🎉 欢迎使用 CKEditor！</h2>

<p>这是一个功能完整的富文本编辑器，支持以下功能：</p>

<ul>
    <li><strong>文本格式化</strong>：粗体、斜体、下划线、删除线</li>
    <li><strong>列表功能</strong>：有序列表、无序列表</li>
    <li><strong>对齐方式</strong>：左对齐、居中、右对齐、两端对齐</li>
    <li><strong>链接插入</strong>：支持外部链接和锚点</li>
    <li><strong>颜色设置</strong>：文字颜色和背景颜色</li>
</ul>

<p style="color: #8B0000; font-size: 18px;">请尝试使用工具栏中的各种功能！</p>

<blockquote>
<p>💡 <em>提示：您可以使用工具栏中的所有功能来编辑这段文本，测试编辑器的完整功能。</em></p>
</blockquote>
                </textarea>
            </div>
            
            <div class="button-group">
                <button type="submit" class="btn">💾 测试保存</button>
                <button type="button" class="btn btn-secondary" onclick="clearTestEditor()">🗑️ 清空内容</button>
                <button type="button" class="btn btn-success" onclick="showSource()">📄 查看源码</button>
            </div>
        </form>
        
        <div class="features">
            <h3>🛠️ CKEditor 功能特色</h3>
            <ul>
                <li>✅ <strong>中文界面</strong> - 完全中文化的用户界面</li>
                <li>✅ <strong>丰富工具栏</strong> - 包含所有常用的编辑功能</li>
                <li>✅ <strong>自动保存</strong> - 每30秒自动保存草稿到本地</li>
                <li>✅ <strong>草稿恢复</strong> - 页面刷新后可恢复未保存的内容</li>
                <li>✅ <strong>响应式设计</strong> - 适配各种屏幕尺寸</li>
                <li>✅ <strong>源码编辑</strong> - 支持直接编辑HTML源码</li>
                <li>✅ <strong>全屏模式</strong> - 支持全屏编辑体验</li>
                <li>✅ <strong>表格支持</strong> - 插入和编辑表格</li>
                <li>✅ <strong>特殊字符</strong> - 插入各种特殊符号</li>
                <li>✅ <strong>水平线</strong> - 插入分隔线</li>
            </ul>
        </div>
        
        <div class="quick-links">
            <h3 style="color: #8B0000; margin-bottom: 20px;">🔗 快速访问</h3>
            <a href="u.php?username=<?php echo urlencode($_SESSION['username']); ?>">📝 编辑我的内容</a>
            <a href="check.php">👁️ 查看内容</a>
            <a href="c.php">🏛️ 管理控制台</a>
            <a href="e.php">🏠 用户中心</a>
        </div>
    </div>
    
    <script src="ckeditor/ckeditor.js"></script>
    <script>
        // 初始化CKEditor - 与u.php相同的配置
        CKEDITOR.replace('test_editor', {
            height: 400,
            language: 'zh-cn',
            toolbar: [
                ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', 'Blockquote'],
                ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                ['Link', 'Unlink', 'Anchor'],
                ['Image', 'Table', 'HorizontalRule', 'SpecialChar'],
                ['TextColor', 'BGColor'],
                ['Styles', 'Format', 'Font', 'FontSize'],
                ['Maximize', 'ShowBlocks', 'Source']
            ],
            removePlugins: 'elementspath',
            resize_enabled: true
        });
        
        // 清空编辑器
        function clearTestEditor() {
            if (confirm('确定要清空编辑器内容吗？')) {
                CKEDITOR.instances.test_editor.setData('');
            }
        }
        
        // 显示源码
        function showSource() {
            const content = CKEDITOR.instances.test_editor.getData();
            const newWindow = window.open('', '_blank', 'width=800,height=600');
            newWindow.document.write(`
                <html>
                <head><title>HTML源码</title></head>
                <body style="font-family: monospace; padding: 20px;">
                <h2>生成的HTML源码：</h2>
                <textarea style="width: 100%; height: 400px; font-family: monospace;">${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</textarea>
                </body>
                </html>
            `);
        }
        
        // 表单提交前获取编辑器内容
        document.getElementById('testForm').addEventListener('submit', function(e) {
            const content = CKEDITOR.instances.test_editor.getData();
            console.log('编辑器内容：', content);
        });
        
        // 编辑器就绪后的回调
        CKEDITOR.instances.test_editor.on('instanceReady', function() {
            console.log('CKEditor 已成功加载！');
        });
    </script>
</body>
</html>
