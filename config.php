<?php
/**
 * 会员管理系统 - 系统配置文件
 * 深红色主题版本 - 自动生成
 */

// 防止直接访问
if (!defined('SYSTEM_ACCESS')) {
    define('SYSTEM_ACCESS', true);
}

// 系统基本信息
define('SYSTEM_NAME', '会员管理系统');
define('VERSION', '2.0');
define('AUTHOR', 'Deep Red Theme');

// 数据库配置
$host = 'localhost';        // 数据库服务器
$user = 'root';        // 数据库用户名
$pass = '111111';        // 数据库密码
$dbname = 'member_system1272702';    // 数据库名

// 系统配置
define('TIMEZONE', 'Asia/Shanghai');
define('CHARSET', 'utf8mb4');
define('DEBUG_MODE', false);

// 设置时区
date_default_timezone_set(TIMEZONE);

// 错误报告设置
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * 创建数据库连接 - PDO方式
 */
function connectDB() {
    global $host, $user, $pass, $dbname;

    try {
        $dsn = "mysql:host=$host;dbname=$dbname;charset=" . CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . CHARSET
        ];

        $pdo = new PDO($dsn, $user, $pass, $options);
        return $pdo;
    } catch (PDOException $e) {
        error_log("数据库连接失败: " . $e->getMessage());
        die("数据库连接失败，请检查配置！");
    }
}

/**
 * 创建数据库连接 - MySQLi方式（兼容旧代码）
 */
function connectMySQLi() {
    global $host, $user, $pass, $dbname;

    $conn = new mysqli($host, $user, $pass, $dbname);

    if ($conn->connect_error) {
        error_log("MySQLi连接失败: " . $conn->connect_error);
        die("数据库连接失败: " . $conn->connect_error);
    }

    $conn->set_charset(CHARSET);
    return $conn;
}

/**
 * 安全的数据库查询
 */
function safe_query($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("SQL查询错误: " . $e->getMessage() . " SQL: " . $sql);
        throw new Exception("数据库查询失败");
    }
}

/**
 * 安全输出HTML内容
 */
function safe_output($content) {
    return htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
}

/**
 * 检查用户登录状态
 */
function check_login() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user']) || !isset($_SESSION['ok'])) {
        header('Location: index.php');
        exit();
    }
}

/**
 * 检查管理员权限
 */
function check_admin() {
    check_login();
    if ($_SESSION['ok'] != 1) {
        header('Location: e.php');
        exit();
    }
}

/**
 * 获取用户信息
 */
function get_user_info($user_id) {
    try {
        $pdo = connectDB();
        $stmt = safe_query($pdo, "SELECT * FROM tab WHERE id = ?", [$user_id]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("获取用户信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取系统统计信息
 */
function get_system_stats() {
    try {
        $pdo = connectDB();

        // 总用户数
        $stmt = safe_query($pdo, "SELECT COUNT(*) as total FROM tab");
        $total_users = $stmt->fetch()['total'];

        // 管理员数
        $stmt = safe_query($pdo, "SELECT COUNT(*) as total FROM tab WHERE ok = 1");
        $admin_users = $stmt->fetch()['total'];

        // 普通用户数
        $stmt = safe_query($pdo, "SELECT COUNT(*) as total FROM tab WHERE ok = 2");
        $normal_users = $stmt->fetch()['total'];

        // 内容数
        $stmt = safe_query($pdo, "SELECT COUNT(*) as total FROM content");
        $total_content = $stmt->fetch()['total'];

        return [
            'total_users' => $total_users,
            'admin_users' => $admin_users,
            'normal_users' => $normal_users,
            'total_content' => $total_content
        ];
    } catch (Exception $e) {
        error_log("获取系统统计失败: " . $e->getMessage());
        return [
            'total_users' => 0,
            'admin_users' => 0,
            'normal_users' => 0,
            'total_content' => 0
        ];
    }
}

/**
 * 记录系统日志
 */
function log_action($action, $details = '') {
    $log_entry = date('Y-m-d H:i:s') . " - " . $action;
    if ($details) {
        $log_entry .= " - " . $details;
    }
    if (isset($_SESSION['user'])) {
        $log_entry .= " - 用户: " . $_SESSION['user'];
    }
    $log_entry .= "\n";

    error_log($log_entry, 3, 'system.log');
}

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

?>