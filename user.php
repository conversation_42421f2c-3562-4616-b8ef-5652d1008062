<?php
// 开启会话
session_start();

// 检查用户是否登录且为管理员
if (!isset($_SESSION['user_id']) || $_SESSION['ok'] != 1) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 获取搜索参数
$search = isset($_GET['search']) ? $_GET['search'] : '';
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';

// 构建查询语句
$sql = "SELECT id, user, ok, time FROM tab WHERE 1=1";

// 添加搜索条件
if (!empty($search)) {
    $sql .= " AND user LIKE '%" . $conn->real_escape_string($search) . "%'";
}

// 添加过滤条件
if (!empty($filter)) {
    if ($filter == 'admin') {
        $sql .= " AND ok = 1";
    } elseif ($filter == 'normal') {
        $sql .= " AND ok = 2";
    }
}

// 执行查询
$result = $conn->query($sql);

// 处理批量操作
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action']) && isset($_POST['selected_users'])) {
        $action = $_POST['action'];
        $selected_users = $_POST['selected_users'];
        
        // 将用户ID转换为整数数组
        $user_ids = array_map('intval', $selected_users);
        $user_ids_str = implode(',', $user_ids);
        
        switch ($action) {
            case 'delete':
                // 删除选中的用户
                $sql = "DELETE FROM tab WHERE id IN ($user_ids_str)";
                if ($conn->query($sql) === TRUE) {
                    $success = "成功删除了 " . count($user_ids) . " 个会员";
                } else {
                    $error = "删除失败: " . $conn->error;
                }
                break;
            
            case 'assign_content':
                // 分配内容给选中的用户（示例功能，实际需要跳转到具体页面）
                // 这里只是展示一个提示信息
                $success = "请选择一个用户以分配内容";
                // 实际应用中可以重定向到指定内容分配页面
                // header("Location: assign_content.php?user_ids=$user_ids_str");
                // exit;
                break;
            
            case 'clear_content':
                // 清除选中的用户的个人内容
                $sql = "DELETE FROM content WHERE user_id IN ($user_ids_str)";
                if ($conn->query($sql) === TRUE) {
                    $success = "已清除 " . count($user_ids) . " 个会员的内容";
                } else {
                    $error = "清除内容失败: " . $conn->error;
                }
                break;
            
            default:
                $error = "未知的操作类型";
        }
        
        // 重新执行查询以获取更新后的数据
        $result = $conn->query($sql);
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>会员管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .form-container {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 5px;
            color: white;
        }
        select {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 5px;
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
        th {
            background-color: rgba(0, 0, 0, 0.3);
        }
        tr:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .checkbox-cell {
            text-align: center;
        }
        .status-admin {
            color: #ffd700;
            font-weight: bold;
        }
        .status-normal {
            color: #add8e6;
        }
        .success {
            background-color: #4CAF50;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .error {
            background-color: #f44336;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
        .action-btn {
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-right: 5px;
        }
        .action-btn:hover {
            background-color: #f2f2f2;
        }
        .nav-link {
            display: block;
            padding: 10px 0;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .nav-link:hover {
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>会员管理</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="#members" class="nav-link">会员列表</a>
            <a href="#add-member" class="nav-link" onclick="window.location.href='add.php'">添加会员</a>
            <a href="#backup" class="nav-link" onclick="window.location.href='backup.php'">数据备份</a>
            <a href="#restore" class="nav-link" onclick="window.location.href='restore.php'">数据恢复</a>
            <a href="#dashboard" class="nav-link" onclick="window.location.href='c.php'">返回控制台</a>
        </div>
        <div class="main-content">
            <h2>会员列表</h2>
            
            <?php if (isset($success)): ?>
                <div class="success"><?php echo $success; ?></div>
            <?php elseif (isset($error)): ?>
                <div class="error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="form-container">
                <form method="get" style="display: flex; gap: 10px; margin-bottom: 20px;">
                    <input type="text" name="search" placeholder="按用户名搜索" value="<?php echo htmlspecialchars($search); ?>">
                    <select name="filter" onchange="this.form.submit()">
                        <option value="">全部用户</option>
                        <option value="admin" <?php echo ($filter == 'admin') ? 'selected' : ''; ?>>管理员</option>
                        <option value="normal" <?php echo ($filter == 'normal') ? 'selected' : ''; ?>>普通用户</option>
                    </select>
                    <input type="submit" value="筛选">
                </form>
                
                <form method="post">
                    <div style="margin-bottom: 15px;">
                        <select name="action" required>
                            <option value="">选择操作</option>
                            <option value="delete">删除选中会员</option>
                            <option value="assign_content">指定内容给选中会员</option>
                            <option value="clear_content">清空选中会员内容</option>
                        </select>
                        <input type="submit" value="执行操作" class="action-btn">
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th class="checkbox-cell"><input type="checkbox" id="select-all"></th>
                                <th>用户名</th>
                                <th>注册时间</th>
                                <th>权限级别</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td class="checkbox-cell">
                                    <input type="checkbox" name="selected_users[]" value="<?php echo $row['id']; ?>">
                                </td>
                                <td><?php echo htmlspecialchars($row['user']); ?></td>
                                <td><?php echo $row['time']; ?></td>
                                <td>
                                    <?php echo ($row['ok'] == 1) ? '管理员' : '普通用户'; ?>
                                </td>
                                <td>
                                    <?php if ($row['ok'] == 1): ?>
                                        <span class="status-admin">VIP管理员</span>
                                    <?php else: ?>
                                        <span class="status-normal">尊贵会员</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="check_user222.php?id=<?php echo $row['id']; ?>" class="action-btn">查看内容</a>
                                    <a href="del.php?id=<?php echo $row['id']; ?>" class="action-btn" onclick="return confirm('确定要删除这个会员吗？')">删除</a>
                                    <a href="u.php?id=<?php echo $row['id']; ?>" class="action-btn">编辑内容</a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </form>
            </div>
        </div>
    </div>
    <script>
        // 全选/取消全选功能
        document.getElementById('select-all').addEventListener('change', function() {
            var checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
            for (var checkbox of checkboxes) {
                checkbox.checked = this.checked;
            }
        });
    </script>
</body>
</html>