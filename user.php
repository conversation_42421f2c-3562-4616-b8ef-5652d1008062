<?php
/**
 * 会员管理系统 - 会员管理页面
 * 深红色主题版本
 */

// 包含系统配置
require_once 'config.php';

// 检查管理员权限
check_admin();

$error = '';
$success = '';

// 获取搜索和筛选参数
$search = trim($_GET['search'] ?? '');
$filter = $_GET['filter'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// 处理批量操作
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && isset($_POST['selected_users'])) {
    $action = $_POST['action'];
    $selected_users = array_map('intval', $_POST['selected_users']);

    if (empty($selected_users)) {
        $error = '请选择要操作的用户！';
    } else {
        try {
            $pdo = connectDB();

            switch ($action) {
                case 'delete':
                    // 防止删除当前登录的管理员
                    $selected_users = array_filter($selected_users, function($id) {
                        return $id != $_SESSION['user_id'];
                    });

                    if (!empty($selected_users)) {
                        $placeholders = str_repeat('?,', count($selected_users) - 1) . '?';
                        $stmt = safe_query($pdo, "DELETE FROM tab WHERE id IN ($placeholders)", $selected_users);
                        $success = '成功删除 ' . $stmt->rowCount() . ' 个用户！';
                        log_action('批量删除用户', '删除用户ID: ' . implode(',', $selected_users));
                    } else {
                        $error = '无法删除当前登录的管理员账户！';
                    }
                    break;

                case 'promote':
                    $placeholders = str_repeat('?,', count($selected_users) - 1) . '?';
                    $stmt = safe_query($pdo, "UPDATE tab SET ok = 1 WHERE id IN ($placeholders)", $selected_users);
                    $success = '成功提升 ' . $stmt->rowCount() . ' 个用户为管理员！';
                    log_action('批量提升用户权限', '用户ID: ' . implode(',', $selected_users));
                    break;

                case 'demote':
                    // 防止降级当前登录的管理员
                    $selected_users = array_filter($selected_users, function($id) {
                        return $id != $_SESSION['user_id'];
                    });

                    if (!empty($selected_users)) {
                        $placeholders = str_repeat('?,', count($selected_users) - 1) . '?';
                        $stmt = safe_query($pdo, "UPDATE tab SET ok = 2 WHERE id IN ($placeholders)", $selected_users);
                        $success = '成功降级 ' . $stmt->rowCount() . ' 个用户为普通用户！';
                        log_action('批量降级用户权限', '用户ID: ' . implode(',', $selected_users));
                    } else {
                        $error = '无法降级当前登录的管理员账户！';
                    }
                    break;

                case 'disable':
                    $selected_users = array_filter($selected_users, function($id) {
                        return $id != $_SESSION['user_id'];
                    });

                    if (!empty($selected_users)) {
                        $placeholders = str_repeat('?,', count($selected_users) - 1) . '?';
                        $stmt = safe_query($pdo, "UPDATE tab SET status = 0 WHERE id IN ($placeholders)", $selected_users);
                        $success = '成功禁用 ' . $stmt->rowCount() . ' 个用户账户！';
                        log_action('批量禁用用户', '用户ID: ' . implode(',', $selected_users));
                    } else {
                        $error = '无法禁用当前登录的管理员账户！';
                    }
                    break;

                case 'enable':
                    $placeholders = str_repeat('?,', count($selected_users) - 1) . '?';
                    $stmt = safe_query($pdo, "UPDATE tab SET status = 1 WHERE id IN ($placeholders)", $selected_users);
                    $success = '成功启用 ' . $stmt->rowCount() . ' 个用户账户！';
                    log_action('批量启用用户', '用户ID: ' . implode(',', $selected_users));
                    break;

                default:
                    $error = '无效的操作类型！';
            }
        } catch (Exception $e) {
            $error = '操作失败：' . $e->getMessage();
            error_log("批量操作错误: " . $e->getMessage());
        }
    }
}

// 构建查询条件
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = 'username LIKE ?';
    $params[] = '%' . $search . '%';
}

if (!empty($filter)) {
    switch ($filter) {
        case 'admin':
            $where_conditions[] = 'ok = 1';
            break;
        case 'normal':
            $where_conditions[] = 'ok = 2';
            break;
        case 'active':
            $where_conditions[] = 'status = 1';
            break;
        case 'disabled':
            $where_conditions[] = 'status = 0';
            break;
    }
}

$where_clause = implode(' AND ', $where_conditions);

// 获取总记录数
try {
    $pdo = connectDB();
    $count_sql = "SELECT COUNT(*) as total FROM tab WHERE $where_clause";
    $stmt = safe_query($pdo, $count_sql, $params);
    $total_records = $stmt->fetch()['total'];
    $total_pages = ceil($total_records / $per_page);

    // 获取用户列表
    $list_sql = "SELECT id, username, ok, status, time, last_login FROM tab WHERE $where_clause ORDER BY id DESC LIMIT ? OFFSET ?";
    $list_params = array_merge($params, [$per_page, $offset]);
    $stmt = safe_query($pdo, $list_sql, $list_params);
    $users = $stmt->fetchAll();

} catch (Exception $e) {
    $error = '获取用户列表失败：' . $e->getMessage();
    $users = [];
    $total_records = 0;
    $total_pages = 0;
    error_log("获取用户列表错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 会员管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-form {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9em;
        }

        .form-group input,
        .form-group select {
            padding: 12px 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.3);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }

        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .table-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .batch-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .batch-actions select {
            padding: 8px 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 15px;
            background: rgba(0, 0, 0, 0.2);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9em;
        }

        tr:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-admin {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B0000;
        }

        .status-user {
            background: linear-gradient(135deg, #87CEEB, #4682B4);
            color: white;
        }

        .status-active {
            background: linear-gradient(135deg, #90EE90, #32CD32);
            color: #006400;
        }

        .status-disabled {
            background: linear-gradient(135deg, #FFB6C1, #FF69B4);
            color: #8B0000;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8em;
            border-radius: 10px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination a,
        .pagination span {
            padding: 10px 15px;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .pagination a:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .pagination .current {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .table-header {
                flex-direction: column;
                gap: 15px;
            }

            .batch-actions {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>👥 会员管理</h1>
            <div class="nav-links">
                <a href="c.php" class="nav-link">返回控制台</a>
                <a href="add.php" class="nav-link">添加会员</a>
                <a href="close.php" class="nav-link">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 搜索和筛选区域 -->
        <div class="search-section">
            <form method="get" class="search-form">
                <div class="form-group">
                    <label for="search">搜索用户</label>
                    <input type="text" id="search" name="search"
                           placeholder="输入用户名搜索"
                           value="<?php echo safe_output($search); ?>">
                </div>

                <div class="form-group">
                    <label for="filter">用户筛选</label>
                    <select id="filter" name="filter">
                        <option value="">全部用户</option>
                        <option value="admin" <?php echo $filter == 'admin' ? 'selected' : ''; ?>>管理员</option>
                        <option value="normal" <?php echo $filter == 'normal' ? 'selected' : ''; ?>>普通用户</option>
                        <option value="active" <?php echo $filter == 'active' ? 'selected' : ''; ?>>正常状态</option>
                        <option value="disabled" <?php echo $filter == 'disabled' ? 'selected' : ''; ?>>已禁用</option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">🔍 搜索</button>
                <a href="user.php" class="btn btn-secondary">🔄 重置</a>
            </form>
        </div>

        <!-- 消息提示 -->
        <?php if ($error): ?>
            <div class="alert alert-error">
                ⚠️ <?php echo safe_output($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                ✅ <?php echo safe_output($success); ?>
            </div>
        <?php endif; ?>

        <!-- 用户列表 -->
        <div class="table-section">
            <div class="table-header">
                <h2>用户列表 (共 <?php echo $total_records; ?> 个用户)</h2>

                <form method="post" class="batch-actions" id="batchForm">
                    <select name="action" id="batchAction" required>
                        <option value="">批量操作</option>
                        <option value="delete">🗑️ 删除用户</option>
                        <option value="promote">⬆️ 提升为管理员</option>
                        <option value="demote">⬇️ 降级为普通用户</option>
                        <option value="disable">🚫 禁用账户</option>
                        <option value="enable">✅ 启用账户</option>
                    </select>
                    <button type="submit" class="btn btn-primary" onclick="return confirmBatchAction()">
                        执行操作
                    </button>
                </form>
            </div>

            <?php if (!empty($users)): ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 50px;">
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>用户名</th>
                            <th>权限</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th>最后登录</th>
                            <th style="width: 200px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="selected_users[]"
                                       value="<?php echo $user['id']; ?>"
                                       form="batchForm"
                                       <?php echo $user['id'] == $_SESSION['user_id'] ? 'disabled title="不能选择当前登录账户"' : ''; ?>>
                            </td>
                            <td>
                                <strong><?php echo safe_output($user['username']); ?></strong>
                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                    <small style="color: #FFD700;">(当前用户)</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $user['ok'] == 1 ? 'status-admin' : 'status-user'; ?>">
                                    <?php echo $user['ok'] == 1 ? '👑 管理员' : '👤 普通用户'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $user['status'] == 1 ? 'status-active' : 'status-disabled'; ?>">
                                    <?php echo $user['status'] == 1 ? '✅ 正常' : '🚫 禁用'; ?>
                                </span>
                            </td>
                            <td><?php echo $user['time']; ?></td>
                            <td>
                                <?php echo $user['last_login'] ? $user['last_login'] : '从未登录'; ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="check.php?id=<?php echo $user['id']; ?>"
                                       class="btn btn-secondary btn-small" title="查看内容">
                                        👁️
                                    </a>
                                    <a href="u.php?id=<?php echo $user['id']; ?>"
                                       class="btn btn-secondary btn-small" title="编辑内容">
                                        ✏️
                                    </a>
                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                    <a href="del.php?id=<?php echo $user['id']; ?>"
                                       class="btn btn-secondary btn-small"
                                       title="删除用户"
                                       onclick="return confirm('确定要删除用户 <?php echo safe_output($user['username']); ?> 吗？')">
                                        🗑️
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=1&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">首页</a>
                    <a href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">上一页</a>
                <?php endif; ?>

                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                for ($i = $start_page; $i <= $end_page; $i++):
                ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">下一页</a>
                    <a href="?page=<?php echo $total_pages; ?>&search=<?php echo urlencode($search); ?>&filter=<?php echo urlencode($filter); ?>">末页</a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <div style="text-align: center; padding: 50px; opacity: 0.7;">
                <h3>😔 没有找到符合条件的用户</h3>
                <p>尝试调整搜索条件或筛选选项</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // 全选/取消全选
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="selected_users[]"]:not([disabled])');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 确认批量操作
        function confirmBatchAction() {
            const action = document.getElementById('batchAction').value;
            const selected = document.querySelectorAll('input[name="selected_users[]"]:checked');

            if (!action) {
                alert('请选择要执行的操作！');
                return false;
            }

            if (selected.length === 0) {
                alert('请选择要操作的用户！');
                return false;
            }

            const actionNames = {
                'delete': '删除',
                'promote': '提升为管理员',
                'demote': '降级为普通用户',
                'disable': '禁用',
                'enable': '启用'
            };

            const actionName = actionNames[action] || action;
            const message = `确定要${actionName} ${selected.length} 个用户吗？\n\n此操作不可撤销！`;

            return confirm(message);
        }

        // 自动提交筛选表单
        document.getElementById('filter').addEventListener('change', function() {
            this.form.submit();
        });

        // 搜索框回车提交
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    </script>
</body>
</html>