<?php
// 会员管理系统 - 高级数据恢复
// 深红色主题版本

require_once 'config.php';
check_permission(1); // 只允许管理员访问

$pdo = connectDB();
$message = '';
$error = '';

// 备份目录
$backup_dir = 'backups';

// 处理恢复请求
if ($_POST && isset($_POST['restore_mode'])) {
    $restore_mode = $_POST['restore_mode'];
    $backup_source = $_POST['backup_source'] ?? '';
    
    try {
        $sql_content = '';
        
        // 获取SQL内容
        if ($backup_source === 'upload' && isset($_FILES['backup_file'])) {
            $uploaded_file = $_FILES['backup_file'];
            if ($uploaded_file['error'] === UPLOAD_ERR_OK) {
                $sql_content = file_get_contents($uploaded_file['tmp_name']);
            }
        } elseif ($backup_source === 'existing') {
            $backup_filename = $_POST['backup_filename'] ?? '';
            $backup_path = $backup_dir . '/' . $backup_filename;
            if (file_exists($backup_path)) {
                $sql_content = file_get_contents($backup_path);
            }
        }
        
        if (empty($sql_content)) {
            $error = '无法读取备份文件内容！';
        } else {
            // 根据恢复模式执行不同的策略
            $pdo->exec("SET FOREIGN_KEY_CHECKS=0");
            
            if ($restore_mode === 'complete') {
                // 完全恢复：先清空现有数据
                $pdo->exec("TRUNCATE TABLE content");
                $pdo->exec("DELETE FROM tab WHERE username != 'admin'"); // 保留admin账户
            } elseif ($restore_mode === 'replace') {
                // 替换恢复：删除表后重建
                $pdo->exec("DROP TABLE IF EXISTS tab");
                $pdo->exec("DROP TABLE IF EXISTS content");
            }
            // merge模式：直接插入，忽略冲突
            
            // 分割并执行SQL语句
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));
            $executed = 0;
            $skipped = 0;
            $errors = [];
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        if ($restore_mode === 'merge') {
                            // 合并模式：将INSERT改为INSERT IGNORE
                            if (preg_match('/^INSERT INTO/i', $statement)) {
                                $statement = preg_replace('/^INSERT INTO/i', 'INSERT IGNORE INTO', $statement);
                            }
                        }
                        
                        $pdo->exec($statement);
                        $executed++;
                    } catch (PDOException $e) {
                        // 记录但不中断执行
                        if ($restore_mode === 'merge' && in_array($e->getCode(), ['23000', '42S01'])) {
                            $skipped++;
                        } else {
                            $errors[] = $e->getMessage();
                        }
                    }
                }
            }
            
            $pdo->exec("SET FOREIGN_KEY_CHECKS=1");
            
            // 生成结果消息
            $result_msg = "数据恢复完成！执行了 $executed 条SQL语句";
            if ($skipped > 0) {
                $result_msg .= "，跳过了 $skipped 条重复记录";
            }
            if (!empty($errors)) {
                $result_msg .= "，但有 " . count($errors) . " 条语句执行失败";
            }
            
            if (count($errors) < count($statements) / 2) {
                $message = $result_msg . "。";
            } else {
                $error = $result_msg . "。错误过多，请检查备份文件格式。";
            }
            
            error_log("管理员 {$_SESSION['username']} 执行了 $restore_mode 模式的数据恢复");
        }
        
    } catch (Exception $e) {
        $error = '数据恢复失败：' . $e->getMessage();
        error_log("数据恢复错误: " . $e->getMessage());
    }
}

// 获取现有备份文件
$backup_files = [];
if (is_dir($backup_dir)) {
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filepath = $backup_dir . '/' . $file;
            $backup_files[] = [
                'name' => $file,
                'size' => filesize($filepath),
                'time' => filemtime($filepath)
            ];
        }
    }
    usort($backup_files, function($a, $b) {
        return $b['time'] - $a['time'];
    });
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 高级数据恢复</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-left: 10px;
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .restore-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .mode-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .mode-card {
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mode-card:hover {
            border-color: #8B0000;
            transform: translateY(-2px);
        }
        
        .mode-card.selected {
            border-color: #8B0000;
            background: rgba(139, 0, 0, 0.05);
        }
        
        .mode-card input[type="radio"] {
            display: none;
        }
        
        .mode-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 10px;
        }
        
        .mode-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .source-selection {
            margin: 30px 0;
        }
        
        .source-tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        
        .source-tab {
            padding: 15px 25px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .source-tab.active {
            background: #8B0000;
            color: white;
        }
        
        .source-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
        
        .source-content.active {
            display: block;
        }
        
        .upload-area {
            border: 3px dashed #8B0000;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .backup-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
        
        .backup-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .backup-item:hover {
            background: #f8f9fa;
        }
        
        .backup-item.selected {
            background: rgba(139, 0, 0, 0.1);
            border-left: 4px solid #8B0000;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🔄 高级数据恢复</h1>
            <div>
                <a href="restore.php">🔙 简单恢复</a>
                <a href="backup.php">💾 数据备份</a>
                <a href="c.php">🏛️ 控制台</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-error">❌ <?php echo safe_output($error); ?></div>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <div class="alert alert-success">✅ <?php echo safe_output($message); ?></div>
        <?php endif; ?>
        
        <div class="restore-panel">
            <h2 style="color: #8B0000; margin-bottom: 20px;">🎯 选择恢复模式</h2>
            
            <form method="POST" enctype="multipart/form-data" id="restoreForm">
                <div class="mode-selection">
                    <div class="mode-card" onclick="selectMode('complete', this)">
                        <input type="radio" name="restore_mode" value="complete" id="mode_complete">
                        <div class="mode-title">🔄 完全恢复</div>
                        <div class="mode-desc">清空现有数据后恢复备份（保留admin账户）。适合完全替换数据。</div>
                    </div>
                    
                    <div class="mode-card" onclick="selectMode('replace', this)">
                        <input type="radio" name="restore_mode" value="replace" id="mode_replace">
                        <div class="mode-title">🔥 替换恢复</div>
                        <div class="mode-desc">删除所有表后重建。适合结构发生变化的备份。</div>
                    </div>
                    
                    <div class="mode-card" onclick="selectMode('merge', this)">
                        <input type="radio" name="restore_mode" value="merge" id="mode_merge">
                        <div class="mode-title">🔗 合并恢复</div>
                        <div class="mode-desc">保留现有数据，只添加新数据。适合增量恢复。</div>
                    </div>
                </div>
                
                <div class="source-selection">
                    <h3 style="color: #8B0000; margin-bottom: 15px;">📁 选择备份源</h3>
                    
                    <div class="source-tabs">
                        <button type="button" class="source-tab active" onclick="switchTab('upload')">📤 上传文件</button>
                        <button type="button" class="source-tab" onclick="switchTab('existing')">📁 现有备份</button>
                    </div>
                    
                    <div class="source-content active" id="upload-content">
                        <input type="hidden" name="backup_source" value="upload">
                        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                            <input type="file" id="fileInput" name="backup_file" accept=".sql" style="display: none;">
                            <div>📤 点击选择 .sql 备份文件</div>
                        </div>
                    </div>
                    
                    <div class="source-content" id="existing-content">
                        <input type="hidden" name="backup_source" value="existing">
                        <?php if (empty($backup_files)): ?>
                            <p style="text-align: center; color: #999;">暂无可用备份文件</p>
                        <?php else: ?>
                            <div class="backup-list">
                                <?php foreach ($backup_files as $index => $file): ?>
                                    <div class="backup-item" onclick="selectBackup('<?php echo safe_output($file['name']); ?>', this)">
                                        <input type="radio" name="backup_filename" value="<?php echo safe_output($file['name']); ?>" 
                                               <?php echo $index === 0 ? 'checked' : ''; ?>>
                                        <div style="font-weight: bold;"><?php echo safe_output($file['name']); ?></div>
                                        <div style="font-size: 12px; color: #666;">
                                            <?php echo number_format($file['size'] / 1024, 1); ?> KB | 
                                            <?php echo date('Y-m-d H:i:s', $file['time']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <button type="submit" class="btn" id="restoreBtn" disabled>
                    🚀 开始恢复
                </button>
            </form>
        </div>
    </div>
    
    <script>
        function selectMode(mode, element) {
            document.querySelectorAll('.mode-card').forEach(card => card.classList.remove('selected'));
            element.classList.add('selected');
            document.getElementById('mode_' + mode).checked = true;
            checkFormReady();
        }
        
        function switchTab(tab) {
            document.querySelectorAll('.source-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.source-content').forEach(c => c.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tab + '-content').classList.add('active');
            
            // 更新backup_source值
            document.querySelector('input[name="backup_source"]').value = tab;
            checkFormReady();
        }
        
        function selectBackup(filename, element) {
            document.querySelectorAll('.backup-item').forEach(item => item.classList.remove('selected'));
            element.classList.add('selected');
            element.querySelector('input[type="radio"]').checked = true;
            checkFormReady();
        }
        
        function checkFormReady() {
            const modeSelected = document.querySelector('input[name="restore_mode"]:checked');
            const source = document.querySelector('input[name="backup_source"]').value;
            let sourceReady = false;
            
            if (source === 'upload') {
                sourceReady = document.getElementById('fileInput').files.length > 0;
            } else {
                sourceReady = document.querySelector('input[name="backup_filename"]:checked');
            }
            
            document.getElementById('restoreBtn').disabled = !(modeSelected && sourceReady);
        }
        
        document.getElementById('fileInput').addEventListener('change', checkFormReady);
        
        document.getElementById('restoreForm').addEventListener('submit', function(e) {
            const mode = document.querySelector('input[name="restore_mode"]:checked').value;
            const modeNames = {
                'complete': '完全恢复',
                'replace': '替换恢复', 
                'merge': '合并恢复'
            };
            
            if (!confirm(`确定要执行 ${modeNames[mode]} 吗？\n\n此操作可能会影响现有数据！`)) {
                e.preventDefault();
            }
        });
        
        // 初始化第一个备份为选中状态
        document.addEventListener('DOMContentLoaded', function() {
            const firstBackup = document.querySelector('.backup-item');
            if (firstBackup) {
                firstBackup.classList.add('selected');
            }
        });
    </script>
</body>
</html>
