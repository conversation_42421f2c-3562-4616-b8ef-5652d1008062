<?php
// 开启会话
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 获取用户ID
$user_id = $_SESSION['user_id'];

// 获取用户信息
$sql = "SELECT user, ok FROM tab WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user_info = $result->fetch_assoc();

// 处理内容保存
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = $conn->real_escape_string($_POST['title']);
    $content = $conn->real_escape_string($_POST['content']);
    
    // 检查是否已经有内容，如果有则更新，否则插入新内容
    $check_sql = "SELECT id FROM content WHERE user_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $user_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        // 更新现有内容
        $update_sql = "UPDATE content SET title = ?, content = ? WHERE user_id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ssi", $title, $content, $user_id);
        
        if ($update_stmt->execute()) {
            $success = "内容更新成功";
        } else {
            $error = "内容更新失败: " . $conn->error;
        }
    } else {
        // 插入新内容
        $insert_sql = "INSERT INTO content (user_id, title, content) VALUES (?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("iss", $user_id, $title, $content);
        
        if ($insert_stmt->execute()) {
            $success = "内容保存成功";
        } else {
            $error = "内容保存失败: " . $conn->error;
        }
    }
}

// 获取用户内容（如果存在）
$sql = "SELECT title, content FROM content WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$content_info = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>编辑内容</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .form-container {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 5px;
            color: white;
        }
        .success {
            background-color: #4CAF50;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .error {
            background-color: #f44336;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
        .submit-btn {
            width: 100%;
            padding: 10px;
            margin-top: 15px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
        }
        .submit-btn:hover {
            background-color: #f2f2f2;
        }
        .nav-link {
            display: block;
            padding: 10px 0;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .nav-link:hover {
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>编辑个人内容</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="#edit-content" class="nav-link">编辑内容</a>
            <a href="#view-content" class="nav-link" onclick="window.location.href='check.php'">查看内容</a>
            <a href="#clear-content" class="nav-link" onclick="window.location.href='udel.php'">清空内容</a>
            <?php if ($_SESSION['ok'] == 1): ?>
                <a href="#dashboard" class="nav-link" onclick="window.location.href='c.php'">返回控制台</a>
            <?php else: ?>
                <a href="#home" class="nav-link" onclick="window.location.href='e.php'">返回主页</a>
            <?php endif; ?>
        </div>
        <div class="main-content">
            <div class="form-container">
                <h2>编辑专属内容</h2>
                
                <?php if (isset($success)): ?>
                    <div class="success"><?php echo $success; ?></div>
                <?php elseif (isset($error)): ?>
                    <div class="error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <form method="post">
                    <label for="title">标题:</label>
                    <input type="text" id="title" name="title" value="<?php echo isset($content_info['title']) ? htmlspecialchars($content_info['title']) : ''; ?>">
                    
                    <textarea name="content" id="editor" rows="15" cols="80">
                        <?php echo isset($content_info['content']) ? $content_info['content'] : '请输入您的专属内容...'; ?>
                    </textarea>
                    <br><br>
                    <input type="submit" value="保存内容" class="submit-btn">
                </form>
            </div>
        </div>
    </div>
    <script>
        // 初始化CKEditor
        CKEDITOR.replace('editor');
    </script>
</body>
</html>
