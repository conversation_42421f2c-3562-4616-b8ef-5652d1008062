<?php
/**
 * 会员管理系统 - 内容编辑页面
 * 深红色主题版本 - 集成CKEditor富文本编辑器
 */

// 包含系统配置
require_once 'config.php';

// 检查用户登录状态
check_login();

$error = '';
$success = '';

// 获取当前用户信息
$current_user = get_user_info($_SESSION['user_id']);

// 获取要编辑的用户ID（管理员可以编辑其他用户的内容）
$target_user_id = $_SESSION['user_id']; // 默认编辑自己的内容
$target_user = $current_user;

// 如果是管理员且指定了用户ID，则编辑指定用户的内容
if ($_SESSION['ok'] == 1 && isset($_GET['id']) && !empty($_GET['id'])) {
    $target_user_id = intval($_GET['id']);
    $target_user = get_user_info($target_user_id);

    if (!$target_user) {
        $error = '指定的用户不存在！';
        $target_user_id = $_SESSION['user_id'];
        $target_user = $current_user;
    }
}

// 处理内容保存
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = $_POST['content'] ?? '';
    $content_type = $_POST['content_type'] ?? 'personal';

    if (empty($title)) {
        $error = '请输入内容标题！';
    } elseif (empty($content)) {
        $error = '请输入内容正文！';
    } else {
        try {
            $pdo = connectDB();

            // 检查是否已经有内容
            $stmt = safe_query($pdo,
                "SELECT id FROM content WHERE user_id = ? AND type = ?",
                [$target_user_id, $content_type]
            );
            $existing = $stmt->fetch();

            if ($existing) {
                // 更新现有内容
                $stmt = safe_query($pdo,
                    "UPDATE content SET title = ?, content = ?, update_time = NOW() WHERE user_id = ? AND type = ?",
                    [$title, $content, $target_user_id, $content_type]
                );
                $success = '内容更新成功！';
                log_action('更新内容', '用户ID: ' . $target_user_id . ', 标题: ' . $title);
            } else {
                // 插入新内容
                $stmt = safe_query($pdo,
                    "INSERT INTO content (user_id, title, content, type) VALUES (?, ?, ?, ?)",
                    [$target_user_id, $title, $content, $content_type]
                );
                $success = '内容创建成功！';
                log_action('创建内容', '用户ID: ' . $target_user_id . ', 标题: ' . $title);
            }
        } catch (Exception $e) {
            $error = '保存失败：' . $e->getMessage();
            error_log("内容保存错误: " . $e->getMessage());
        }
    }
}

// 获取现有内容
try {
    $pdo = connectDB();
    $stmt = safe_query($pdo,
        "SELECT * FROM content WHERE user_id = ? ORDER BY time DESC",
        [$target_user_id]
    );
    $existing_contents = $stmt->fetchAll();

    // 获取最新的个人内容作为默认编辑内容
    $default_content = null;
    foreach ($existing_contents as $content_item) {
        if ($content_item['type'] == 'personal') {
            $default_content = $content_item;
            break;
        }
    }
} catch (Exception $e) {
    $existing_contents = [];
    $default_content = null;
    error_log("获取内容错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 内容编辑</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .editor-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 2em;
            margin-bottom: 30px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .editor-container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .btn {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-right: 15px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }

        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .content-list {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .content-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .content-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .content-item h4 {
            color: #FFD700;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .content-meta {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .content-preview {
            opacity: 0.9;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .editor-section {
                padding: 25px;
            }
        }
    </style>
</head>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>✏️ 内容编辑</h1>
            <div class="nav-links">
                <?php if ($current_user['ok'] == 1): ?>
                    <a href="c.php" class="nav-link">返回控制台</a>
                    <a href="user.php" class="nav-link">会员管理</a>
                <?php else: ?>
                    <a href="e.php" class="nav-link">返回主页</a>
                <?php endif; ?>
                <a href="check.php?id=<?php echo $target_user_id; ?>" class="nav-link">查看内容</a>
                <a href="close.php" class="nav-link">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 内容编辑区域 -->
        <div class="editor-section">
            <h2 class="section-title">
                📝 编辑内容
                <?php if ($target_user_id != $_SESSION['user_id']): ?>
                    - <?php echo safe_output($target_user['username']); ?>
                <?php endif; ?>
            </h2>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    ⚠️ <?php echo safe_output($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo safe_output($success); ?>
                </div>
            <?php endif; ?>

            <form method="post" id="contentForm">
                <div class="form-group">
                    <label for="title">内容标题</label>
                    <input type="text" id="title" name="title"
                           placeholder="请输入内容标题"
                           value="<?php echo safe_output($default_content['title'] ?? $_POST['title'] ?? ''); ?>"
                           required>
                </div>

                <div class="form-group">
                    <label for="content_type">内容类型</label>
                    <select id="content_type" name="content_type">
                        <option value="personal" <?php echo ($_POST['content_type'] ?? 'personal') == 'personal' ? 'selected' : ''; ?>>
                            个人内容（仅自己可见）
                        </option>
                        <?php if ($current_user['ok'] == 1): ?>
                        <option value="general" <?php echo ($_POST['content_type'] ?? 'personal') == 'general' ? 'selected' : ''; ?>>
                            通用内容（所有用户可见）
                        </option>
                        <?php endif; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="editor">内容正文</label>
                    <div class="editor-container">
                        <textarea name="content" id="editor" rows="20" cols="80">
                            <?php echo $default_content['content'] ?? $_POST['content'] ?? '请输入您的内容...'; ?>
                        </textarea>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button type="submit" class="btn">
                        💾 保存内容
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="previewContent()">
                        👁️ 预览内容
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearContent()">
                        🗑️ 清空内容
                    </button>
                </div>
            </form>
        </div>

        <!-- 现有内容列表 -->
        <?php if (!empty($existing_contents)): ?>
        <div class="content-list">
            <h2 class="section-title">📋 现有内容</h2>
            <?php foreach ($existing_contents as $content_item): ?>
            <div class="content-item">
                <h4><?php echo safe_output($content_item['title']); ?></h4>
                <div class="content-meta">
                    类型：<?php echo $content_item['type'] == 'general' ? '通用内容' : '个人内容'; ?> |
                    创建时间：<?php echo $content_item['time']; ?> |
                    更新时间：<?php echo $content_item['update_time']; ?>
                </div>
                <div class="content-preview">
                    <?php
                    $preview = strip_tags($content_item['content']);
                    echo safe_output(mb_substr($preview, 0, 200) . (mb_strlen($preview) > 200 ? '...' : ''));
                    ?>
                </div>
                <div style="margin-top: 10px;">
                    <button class="btn btn-secondary" onclick="loadContent('<?php echo $content_item['id']; ?>')">
                        📝 编辑此内容
                    </button>
                    <button class="btn btn-secondary" onclick="deleteContent('<?php echo $content_item['id']; ?>')">
                        🗑️ 删除
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // 初始化CKEditor
        CKEDITOR.replace('editor', {
            height: 500,
            language: 'zh-cn',
            toolbar: [
                ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript'],
                ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', 'Blockquote'],
                ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                ['Link', 'Unlink', 'Anchor'],
                ['Image', 'Table', 'HorizontalRule', 'SpecialChar'],
                ['TextColor', 'BGColor'],
                ['Styles', 'Format', 'Font', 'FontSize'],
                ['Maximize', 'ShowBlocks'],
                ['Source']
            ],
            filebrowserUploadUrl: 'upload.php', // 如果需要图片上传功能
            removeDialogTabs: 'image:advanced;link:advanced'
        });

        // 预览内容
        function previewContent() {
            const title = document.getElementById('title').value;
            const content = CKEDITOR.instances.editor.getData();

            if (!title || !content) {
                alert('请先填写标题和内容！');
                return;
            }

            const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>内容预览</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        h1 { color: #8B0000; border-bottom: 2px solid #8B0000; padding-bottom: 10px; }
                        .content { margin-top: 20px; }
                    </style>
                </head>
                <body>
                    <h1>${title}</h1>
                    <div class="content">${content}</div>
                </body>
                </html>
            `);
            previewWindow.document.close();
        }

        // 清空内容
        function clearContent() {
            if (confirm('确定要清空所有内容吗？此操作不可撤销！')) {
                document.getElementById('title').value = '';
                CKEDITOR.instances.editor.setData('');
            }
        }

        // 加载指定内容到编辑器
        function loadContent(contentId) {
            // 这里可以通过AJAX加载指定内容
            // 为简化，这里只是提示
            alert('功能开发中：加载内容ID ' + contentId);
        }

        // 删除内容
        function deleteContent(contentId) {
            if (confirm('确定要删除这个内容吗？此操作不可撤销！')) {
                // 这里可以通过AJAX删除内容
                // 为简化，这里只是提示
                alert('功能开发中：删除内容ID ' + contentId);
            }
        }

        // 表单验证
        document.getElementById('contentForm').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const content = CKEDITOR.instances.editor.getData().trim();

            if (!title) {
                e.preventDefault();
                alert('请输入内容标题！');
                document.getElementById('title').focus();
                return false;
            }

            if (!content || content === '<p>请输入您的内容...</p>') {
                e.preventDefault();
                alert('请输入内容正文！');
                CKEDITOR.instances.editor.focus();
                return false;
            }

            return true;
        });

        // 自动保存草稿
        let autoSaveTimer;
        function autoSave() {
            const title = document.getElementById('title').value;
            const content = CKEDITOR.instances.editor.getData();

            if (title || content) {
                localStorage.setItem('draft_title_<?php echo $target_user_id; ?>', title);
                localStorage.setItem('draft_content_<?php echo $target_user_id; ?>', content);
                console.log('草稿已自动保存');
            }
        }

        // 每30秒自动保存一次草稿
        setInterval(autoSave, 30000);

        // 恢复草稿
        window.addEventListener('load', function() {
            const draftTitle = localStorage.getItem('draft_title_<?php echo $target_user_id; ?>');
            const draftContent = localStorage.getItem('draft_content_<?php echo $target_user_id; ?>');

            if (draftTitle && !document.getElementById('title').value) {
                if (confirm('发现未保存的草稿，是否恢复？')) {
                    document.getElementById('title').value = draftTitle;
                    if (draftContent) {
                        CKEDITOR.instances.editor.setData(draftContent);
                    }
                }
            }
        });

        // 成功保存后清除草稿
        <?php if ($success): ?>
        localStorage.removeItem('draft_title_<?php echo $target_user_id; ?>');
        localStorage.removeItem('draft_content_<?php echo $target_user_id; ?>');
        <?php endif; ?>
    </script>
</body>
</html>
