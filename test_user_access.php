<?php
// 开启会话
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 获取用户ID
$user_id = $_SESSION['user_id'];

// 获取用户信息
$sql = "SELECT user, ok FROM tab WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user_info = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>用户访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .container {
            display: flex;
            min-height: 100vh;
            justify-content: center;
            align-items: center;
        }
        .main-content {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        h2 {
            margin-bottom: 20px;
        }
        .info-item {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }
        .label {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
            display: block;
        }
        .value {
            font-size: 16px;
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
        }
        .status-admin {
            color: #ffd700;
            font-weight: bold;
        }
        .status-normal {
            color: #add8e6;
        }
        .action-btn {
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 20px;
        }
        .action-btn:hover {
            background-color: #f2f2f2;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>用户访问测试</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="main-content">
            <h2>访问成功</h2>
            <p>您已成功访问受保护的页面。以下是您的账户信息：</p>
            
            <div class="info-item">
                <span class="label">用户名：</span>
                <span class="value"><?php echo htmlspecialchars($user_info['user']); ?></span>
                
                <span class="label">用户ID：</span>
                <span class="value"><?php echo $user_id; ?></span>
                
                <span class="label">权限级别：</span>
                <span class="value">
                    <?php if ($user_info['ok'] == 1): ?>
                        <span class="status-admin">管理员 (VIP管理员)</span>
                    <?php else: ?>
                        <span class="status-normal">普通用户 (尊贵会员)</span>
                    <?php endif; ?>
                </span>
                
                <span class="label">登录时间：</span>
                <span class="value"><?php echo date('Y-m-d H:i:s'); ?></span>
            </div>
            
            <a href="index.php" class="action-btn">返回首页</a>
        </div>
    </div>
</body>
</html>