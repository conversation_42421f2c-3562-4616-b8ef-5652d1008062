<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>CKEditor Samples</title>
	<meta content="text/html; charset=utf-8" http-equiv="content-type" />
	<link type="text/css" rel="stylesheet" href="sample.css" />
</head>
<body>
	<h1 class="samples">
		CKEditor Samples Site
	</h1>
	<h2 class="samples">
		Basic Samples
	</h2>
	<ul class="samples">
		<li>
			<a class="samples" href="replacebyclass.html">Replace textarea elements by class name</a><br />
			Automatic replacement of all textarea elements of a given class with a CKEditor instance.
		</li>
		<li><a class="samples" href="replacebycode.html">Replace textarea elements by code</a><br />
			Replacement of textarea elements with CKEditor instances by using a JavaScript call.
		</li>
		<li><a class="samples" href="fullpage.html">Full page support with the Document Properties plugin</a><br />
			CKEditor inserted with a JavaScript call and used to edit the whole page from <code>&lt;html&gt;</code> to <code>&lt;/html&gt;</code>.
		</li>
	</ul>
	<h2 class="samples">
		Basic Customization
	</h2>
	<ul class="samples">
		<li><a class="samples" href="skins.html">Skins</a><br />
			Changing the CKEditor skin by adjusting a single configuration option.
		</li>
		<li><a class="samples" href="ui_color.html">User Interface color</a><br />
			Changing CKEditor User Interface color and adding a toolbar button that lets the user set the UI color.
		</li>
		<li><a class="samples" href="ui_languages.html">User Interface languages</a><br />
			Changing CKEditor User Interface language and adding a drop-down list that lets the user choose the UI language.
		</li>
	</ul>
	<h2 class="samples">
		Advanced Samples
	</h2>
	<ul class="samples">
		<li><a class="samples" href="divreplace.html">Replace DIV elements on the fly</a><br />
			Transforming a <code>div</code> element into an instance of CKEditor with a mouse click.
		</li>
		<li><a class="samples" href="ajax.html">Create and destroy editor instances for Ajax applications</a><br />
			Creating and destroying CKEditor instances on the fly and saving the contents entered into the editor window.
		</li>
		<li><a class="samples" href="api.html">Basic usage of the API</a><br />
			Using the CKEditor JavaScript API to interact with the editor at runtime.
		</li>
		<li><a class="samples" href="api_dialog.html">Using the JavaScript API to customize dialog windows</a><br />
			Using the dialog windows API to customize dialog windows without changing the original editor code.
		</li>
		<li><a class="samples" href="enterkey.html">Using the "Enter" key in CKEditor</a><br />
			 Configuring the behavior of <em>Enter</em> and <em>Shift+Enter</em> keys.
		</li>
		<li><a class="samples" href="sharedspaces.html">Shared toolbars</a><br />
			Displaying multiple editor instances that share the toolbar and/or the elements path.
		</li>
		<li><a class="samples" href="jqueryadapter.html">jQuery adapter example</a><br />
			Using the jQuery adapter to configure CKEditor.
		</li>
		<li><a class="samples" href="output_xhtml.html">Output XHTML</a><br />
			Configuring CKEditor to produce XHTML 1.1 compliant code.
		</li>
		<li><a class="samples" href="output_html.html">Output HTML</a><br />
			Configuring CKEditor to produce legacy HTML 4 code.
		</li>
		<li><a class="samples" href="output_for_flash.html">Output for Flash</a><br />
			Configuring CKEditor to produce HTML code that can be used with Adobe Flash.
		</li>
		<li><a class="samples" href="readonly.html">Read-only mode</a><br />
			Using the readOnly API to block introducing changes to the editor contents.
		</li>
	</ul>
	<h2 class="samples">
		Additional plugins
	</h2>
	<ul class="samples">
		<li><a class="samples" href="autogrow.html">AutoGrow plugin</a><br />
			Using the AutoGrow plugin in order to make the editor grow to fit the size of its content.
		</li>
		<li><a class="samples" href="bbcode.html">Output for BBCode</a><br />
			Configuring CKEditor to produce BBCode tags instead of HTML.
		</li>
		<li><a class="samples" href="stylesheetparser.html">Stylesheet Parser plugin</a><br />
			Using the Stylesheet Parser plugin to fill the Styles drop-down list based on the CSS classes available in the document stylesheet.
		</li>
		<li><a class="samples" href="devtools.html">Developer Tools plugin</a><br />
			Using the Developer Tools plugin to display information about dialog window UI elements to allow for easier customization.
		</li>
		<li><a class="samples" href="placeholder.html">Placeholder plugin</a><br />
			Using the Placeholder plugin to create uneditable sections that can only be created and modified with a proper dialog window.
		</li>
		<li><a class="samples" href="tableresize.html">TableResize plugin</a><br />
			Using the TableResize plugin to enable table column resizing.
		</li>
	</ul>
	<div id="footer">
		<hr />
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2011, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
