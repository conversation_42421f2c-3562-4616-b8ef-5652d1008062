-- 会员管理系统数据库结构
-- 深红色主题版本

-- 创建用户表
CREATE TABLE `tab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码(MD5加密)',
  `ok` int(11) NOT NULL DEFAULT '2' COMMENT '用户权限(1=管理员,2=普通用户)',
  `time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `last_login` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '账户状态(1=正常,0=禁用)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `ok` (`ok`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建内容表
CREATE TABLE `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '内容标题',
  `content` longtext NOT NULL COMMENT '内容正文',
  `type` varchar(20) NOT NULL DEFAULT 'general' COMMENT '内容类型(general=通用,personal=个人)',
  `time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '内容状态(1=正常,0=隐藏)',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `time` (`time`),
  CONSTRAINT `content_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tab` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容表';

-- 创建系统日志表
CREATE TABLE `system_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '操作用户ID',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `details` text NULL DEFAULT NULL COMMENT '操作详情',
  `ip_address` varchar(45) NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text NULL DEFAULT NULL COMMENT '用户代理',
  `time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `time` (`time`),
  CONSTRAINT `system_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `tab` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入默认管理员账户
INSERT INTO `tab` (`username`, `password`, `ok`, `status`) VALUES
('admin', MD5('111111'), 1, 1);

-- 插入默认系统内容
INSERT INTO `content` (`user_id`, `title`, `content`, `type`) VALUES
(1, '欢迎使用会员管理系统', '<h2>欢迎使用会员管理系统</h2><p>这是一个功能完整的会员管理系统，采用深红色主题设计。</p><h3>主要功能：</h3><ul><li>用户注册与登录</li><li>会员权限管理</li><li>内容发布与管理</li><li>数据备份与恢复</li><li>系统日志记录</li></ul><p>默认管理员账户：<br>用户名：admin<br>密码：111111</p>', 'general');