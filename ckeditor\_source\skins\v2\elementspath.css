/*
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

@media print
{
	.cke_path
	{
		display: none;
	}
}

.cke_skin_v2 .cke_path
{
	padding: 3px 3px 0 3px;
	display: inline-block;
	float: left;
}

.cke_skin_v2 .cke_rtl .cke_path
{
	float: right;
}

.cke_skin_v2 .cke_path a,
.cke_skin_v2 .cke_path .cke_empty
{
	display: inline-block;
	float: left;
	border: solid 1px #efefde;
	background-color: #efefde;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 4px;
	padding-right: 4px;
	margin-bottom : 3px;
	cursor: default;
}

.cke_skin_v2 .cke_path .cke_empty
{
	visibility: hidden;
}

.cke_skin_v2 .cke_rtl .cke_path a,
.cke_skin_v2 .cke_rtl .cke_path cke_empty
{
	float: right;
}

.cke_skin_v2 .cke_path a:hover,
.cke_skin_v2 .cke_path a:focus,
.cke_skin_v2 .cke_path a:active	/* IE */
{
	border: solid 1px #316ac5;
	background-color: #dff1ff;
	padding-top: 1px;
	padding-bottom: 1px;
	padding-left: 4px;
	padding-right: 4px;
	outline: none;
}

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_path a,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_path .cke_empty
{
	float: none;
}

.cke_skin_v2 .cke_path .cke_label
{
	display: none;
}
