<?php
// 会员管理系统 - 登录页面
// 深红色主题版本

session_start();
require_once 'config.php';

// 如果已经登录，跳转到对应页面
if (isset($_SESSION['username']) && isset($_SESSION['ok'])) {
    if ($_SESSION['ok'] == 1) {
        header('Location: c.php'); // 管理员页面
    } else {
        header('Location: e.php'); // 普通用户页面
    }
    exit();
}

$error = '';
$success = '';

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $user = $_POST['user'] ?? '';
    $pass = md5($_POST['pass']) ?? ''; // 使用MD5加密密码
    
    if (empty($user) || empty($pass)) {
        $error = '请输入用户名和密码！';
    } else {
        try {
            $pdo = connectDB();
            $stmt = safe_query($pdo, "SELECT id, username, ok FROM tab WHERE username = ? AND password = ?", [$user, $pass]);
            $user = $stmt->fetch();
            
            if ($user) {
                // 登录成功
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user'] = $user['username'];
                $_SESSION['ok'] = $user['ok'];
                
                // 根据用户权限跳转
                if ($user['ok'] == 1) {
                    header('Location: c.php'); // 管理员
                } else {
                    header('Location: e.php'); // 普通用户
                }
                exit();
            } else {
                $error = '用户名或密码错误！';
            }
        } catch (Exception $e) {
            $error = '登录失败，请稍后重试！';
            error_log("登录错误: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px 40px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            max-width: 450px;
            width: 90%;
            position: relative;
            transform: translateY(0);
            transition: all 0.3s ease;
        }
        
        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 35px 70px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .login-header h1 {
            color: #8B0000;
            font-size: 2.8em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            font-weight: 700;
        }
        
        .login-header p {
            color: #666;
            font-size: 1.1em;
            opacity: 0.8;
        }
        
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #8B0000;
            box-shadow: 0 0 20px rgba(139, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
            transform: scale(1.02);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
            color: white;
            padding: 18px 30px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-login:hover::before {
            left: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn-login:active {
            transform: translateY(-1px);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .system-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(139, 0, 0, 0.2);
        }
        
        .system-info p {
            color: #666;
            font-size: 12px;
            margin: 5px 0;
        }
        
        .install-link {
            display: inline-block;
            margin-top: 20px;
            color: #8B0000;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border: 2px solid #8B0000;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .install-link:hover {
            background: #8B0000;
            color: white;
            transform: translateY(-2px);
        }
        
        @media (max-width: 480px) {
            .login-container {
                padding: 30px 25px;
                margin: 20px;
            }
            
            .login-header h1 {
                font-size: 2.2em;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>会员管理系统</h1>
        </div>
        
        <?php if ($error): ?>
            <div class="error">
                <?php echo safe_output($error); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="user">用户名</label>
                <input type="text" id="user" name="user" required 
                       value="<?php echo safe_output($_POST['user'] ?? ''); ?>"
                       placeholder="请输入用户名">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="pass" name="pass" required 
                       placeholder="请输入密码">
            </div>
            
            <button type="submit" class="btn-login">登录</button>
        </form>
        
        <div class="system-info">
            <p>当前系统名称：<?php echo SYSTEM_NAME; ?> v<?php echo VERSION; ?></p>
            <p>当前时间：<?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
    
    <script>
        // 自动聚焦到用户名输入框
        document.getElementById('username').focus();
        
        // 回车键快速登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.btn-login').click();
            }
        });
        
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('请填写完整的登录信息！');
                return false;
            }
        });
    </script>
</body>
</html>
