<?php
/**
 * 会员管理系统 - 管理员控制台
 * 深红色主题版本
 */

// 包含系统配置
require_once 'config.php';

// 检查管理员权限
check_admin();

$error = '';
$success = '';

// 获取系统统计信息
$stats = get_system_stats();

// 获取当前用户信息
$current_user = get_user_info($_SESSION['user_id']);

// 处理内容发布
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['publish_content'])) {
    $title = trim($_POST['title'] ?? '');
    $content = $_POST['content'] ?? '';
    $content_type = $_POST['content_type'] ?? 'general';

    if (empty($title) || empty($content)) {
        $error = '请填写完整的内容信息！';
    } else {
        try {
            $pdo = connectDB();
            $stmt = safe_query($pdo,
                "INSERT INTO content (user_id, title, content, type) VALUES (?, ?, ?, ?)",
                [$_SESSION['user_id'], $title, $content, $content_type]
            );

            $success = '内容发布成功！';
            log_action('发布内容', '标题: ' . $title . ', 类型: ' . $content_type);

            // 重新获取统计信息
            $stats = get_system_stats();

        } catch (Exception $e) {
            $error = '内容发布失败：' . $e->getMessage();
            error_log("内容发布错误: " . $e->getMessage());
        }
    }
}

// 获取最新内容
try {
    $pdo = connectDB();
    $stmt = safe_query($pdo,
        "SELECT c.*, t.username FROM content c
         LEFT JOIN tab t ON c.user_id = t.id
         ORDER BY c.time DESC LIMIT 5"
    );
    $recent_content = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_content = [];
    error_log("获取最新内容错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 管理员控制台</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-welcome {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            border: none;
            padding: 12px 25px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .stat-icon {
            font-size: 3em;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 2em;
            margin-bottom: 30px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .btn-primary {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            padding: 15px 30px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }

        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .navigation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .nav-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-decoration: none;
            color: white;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .nav-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .nav-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .nav-desc {
            font-size: 0.9em;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .dashboard-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .navigation-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🏛️ 管理员控制台</h1>
            <div class="user-info">
                <div class="user-welcome">
                    欢迎，<?php echo safe_output($current_user['username'] ?? '管理员'); ?>
                </div>
                <a href="close.php" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 系统统计仪表盘 -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-value"><?php echo $stats['total_users']; ?></div>
                <div class="stat-label">总会员数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👑</div>
                <div class="stat-value"><?php echo $stats['admin_users']; ?></div>
                <div class="stat-label">管理员数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👤</div>
                <div class="stat-value"><?php echo $stats['normal_users']; ?></div>
                <div class="stat-label">普通用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📄</div>
                <div class="stat-value"><?php echo $stats['total_content']; ?></div>
                <div class="stat-label">内容总数</div>
            </div>
        </div>

        <!-- 内容发布区域 -->
        <div class="content-section">
            <h2 class="section-title">📝 内容发布</h2>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    ⚠️ <?php echo safe_output($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo safe_output($success); ?>
                </div>
            <?php endif; ?>

            <form method="post">
                <div class="form-group">
                    <label for="title">内容标题</label>
                    <input type="text" id="title" name="title"
                           placeholder="请输入内容标题"
                           value="<?php echo safe_output($_POST['title'] ?? ''); ?>" required>
                </div>

                <div class="form-group">
                    <label for="content_type">内容类型</label>
                    <select id="content_type" name="content_type">
                        <option value="general" <?php echo ($_POST['content_type'] ?? '') == 'general' ? 'selected' : ''; ?>>
                            通用内容（所有用户可见）
                        </option>
                        <option value="personal" <?php echo ($_POST['content_type'] ?? '') == 'personal' ? 'selected' : ''; ?>>
                            个人内容（指定用户可见）
                        </option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="editor">内容正文</label>
                    <textarea name="content" id="editor" rows="15" cols="80">
                        <?php echo safe_output($_POST['content'] ?? '请输入内容...'); ?>
                    </textarea>
                </div>

                <button type="submit" name="publish_content" class="btn-primary">
                    📤 发布内容
                </button>
            </form>
        </div>

        <!-- 快速导航 -->
        <div class="navigation-grid">
            <a href="user.php" class="nav-card">
                <div class="nav-icon">👥</div>
                <div class="nav-title">会员管理</div>
                <div class="nav-desc">管理系统用户</div>
            </a>
            <a href="add.php" class="nav-card">
                <div class="nav-icon">➕</div>
                <div class="nav-title">添加会员</div>
                <div class="nav-desc">注册新用户</div>
            </a>
            <a href="backup.php" class="nav-card">
                <div class="nav-icon">💾</div>
                <div class="nav-title">数据备份</div>
                <div class="nav-desc">备份系统数据</div>
            </a>
            <a href="restore.php" class="nav-card">
                <div class="nav-icon">🔄</div>
                <div class="nav-title">数据恢复</div>
                <div class="nav-desc">恢复系统数据</div>
            </a>
        </div>

        <!-- 最新内容预览 -->
        <?php if (!empty($recent_content)): ?>
        <div class="content-section">
            <h2 class="section-title">📋 最新内容</h2>
            <div style="display: grid; gap: 15px;">
                <?php foreach ($recent_content as $content): ?>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                    <h4 style="margin-bottom: 10px; color: #FFD700;">
                        <?php echo safe_output($content['title']); ?>
                    </h4>
                    <p style="opacity: 0.8; font-size: 0.9em; margin-bottom: 10px;">
                        作者：<?php echo safe_output($content['username']); ?> |
                        时间：<?php echo $content['time']; ?> |
                        类型：<?php echo $content['type'] == 'general' ? '通用' : '个人'; ?>
                    </p>
                    <div style="opacity: 0.9;">
                        <?php
                        $preview = strip_tags($content['content']);
                        echo safe_output(mb_substr($preview, 0, 100) . (mb_strlen($preview) > 100 ? '...' : ''));
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // 初始化CKEditor
        CKEDITOR.replace('editor', {
            height: 400,
            language: 'zh-cn',
            toolbar: [
                ['Bold', 'Italic', 'Underline', 'Strike'],
                ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'],
                ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                ['Link', 'Unlink'],
                ['Image', 'Table', 'HorizontalRule'],
                ['TextColor', 'BGColor'],
                ['Styles', 'Format', 'Font', 'FontSize'],
                ['Source']
            ]
        });

        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const content = CKEDITOR.instances.editor.getData().trim();

            if (!title || !content || content === '<p>请输入内容...</p>') {
                e.preventDefault();
                alert('请填写完整的内容信息！');
                return false;
            }
        });

        // 自动保存草稿（可选功能）
        let autoSaveTimer;
        function autoSave() {
            const title = document.getElementById('title').value;
            const content = CKEDITOR.instances.editor.getData();

            if (title || content) {
                localStorage.setItem('draft_title', title);
                localStorage.setItem('draft_content', content);
            }
        }

        // 恢复草稿
        window.addEventListener('load', function() {
            const draftTitle = localStorage.getItem('draft_title');
            const draftContent = localStorage.getItem('draft_content');

            if (draftTitle && !document.getElementById('title').value) {
                document.getElementById('title').value = draftTitle;
            }

            if (draftContent && CKEDITOR.instances.editor.getData().trim() === '<p>请输入内容...</p>') {
                CKEDITOR.instances.editor.setData(draftContent);
            }
        });

        // 清除草稿
        document.querySelector('form').addEventListener('submit', function() {
            localStorage.removeItem('draft_title');
            localStorage.removeItem('draft_content');
        });
    </script>
</body>
</html>