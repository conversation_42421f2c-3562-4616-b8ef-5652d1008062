<?php
// 开启会话
session_start();

// 检查用户是否登录且为管理员
if (!isset($_SESSION['user_id']) || $_SESSION['ok'] != 1) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 获取统计信息
$stats = array();

// 总会员数量
$sql = "SELECT COUNT(*) as total FROM tab";
$result = $conn->query($sql);
$row = $result->fetch_assoc();
$stats['total_users'] = $row['total'];

// 管理员数量
$sql = "SELECT COUNT(*) as total FROM tab WHERE ok = 1";
$result = $conn->query($sql);
$row = $result->fetch_assoc();
$stats['admin_users'] = $row['total'];

// 普通用户数量
$sql = "SELECT COUNT(*) as total FROM tab WHERE ok = 2";
$result = $conn->query($sql);
$row = $result->fetch_assoc();
$stats['normal_users'] = $row['total'];

// 当前时间
$stats['current_time'] = date('Y-m-d H:i:s');

// 处理内容发布
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['publish_content'])) {
    $title = $conn->real_escape_string($_POST['title']);
    $content = $conn->real_escape_string($_POST['content']);
    $user_id = $_SESSION['user_id'];
    
    $sql = "INSERT INTO content (user_id, title, content) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", $user_id, $title, $content);
    
    if ($stmt->execute()) {
        $publish_success = "内容发布成功";
        // 重新获取统计数据
        $sql = "SELECT COUNT(*) as total FROM content WHERE user_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['user_content_count'] = $row['total'];
    } else {
        $publish_error = "内容发布失败: " . $conn->error;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>管理员控制台</title>
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to right, #8e0000, #ff4e50);
            color: white;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
        }
        .container {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 20px;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            padding: 20px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        .card-title {
            font-size: 16px;
            margin-bottom: 10px;
            opacity: 0.8;
        }
        .card-value {
            font-size: 24px;
            font-weight: bold;
        }
        .form-container {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 5px;
            color: white;
        }
        .success {
            background-color: #4CAF50;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .error {
            background-color: #f44336;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #ffffff;
            color: #8e0000;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .logout-btn:hover {
            background-color: #f2f2f2;
        }
        .nav-link {
            display: block;
            padding: 10px 0;
            color: white;
            text-decoration: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .nav-link:hover {
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>管理员控制台</h1>
        <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
    </div>
    <div class="container">
        <div class="sidebar">
            <a href="#dashboard" class="nav-link">仪表盘</a>
            <a href="#publish" class="nav-link">内容发布</a>
            <a href="#members" class="nav-link" onclick="window.location.href='user.php'">会员管理</a>
            <a href="#add-member" class="nav-link" onclick="window.location.href='add.php'">添加会员</a>
            <a href="#backup" class="nav-link" onclick="window.location.href='backup.php'">数据备份</a>
            <a href="#restore" class="nav-link" onclick="window.location.href='restore.php'">数据恢复</a>
        </div>
        <div class="main-content">
            <section id="dashboard">
                <h2>系统概览</h2>
                <div class="stats">
                    <div class="stat-card">
                        <div class="card-title">总会员数量</div>
                        <div class="card-value"><?php echo $stats['total_users']; ?></div>
                    </div>
                    <div class="stat-card">
                        <div class="card-title">管理员数量</div>
                        <div class="card-value"><?php echo $stats['admin_users']; ?></div>
                    </div>
                    <div class="stat-card">
                        <div class="card-title">普通用户数量</div>
                        <div class="card-value"><?php echo $stats['normal_users']; ?></div>
                    </div>
                    <div class="stat-card">
                        <div class="card-title">当前时间</div>
                        <div class="card-value"><?php echo $stats['current_time']; ?></div>
                    </div>
                </div>
            </section>
            
            <section id="publish">
                <h2>内容发布</h2>
                <?php if (isset($publish_success)): ?>
                    <div class="success"><?php echo $publish_success; ?></div>
                <?php elseif (isset($publish_error)): ?>
                    <div class="error"><?php echo $publish_error; ?></div>
                <?php endif; ?>
                <div class="form-container">
                    <form method="post">
                        <input type="text" name="title" placeholder="请输入标题" required>
                        <textarea name="content" id="editor" rows="10" cols="80">
                            请输入内容...
                        </textarea>
                        <br><br>
                        <input type="submit" name="publish_content" value="发布内容">
                    </form>
                </div>
            </section>
        </div>
    </div>
    <script>
        // 初始化CKEditor
        CKEDITOR.replace('editor');
    </script>
</body>
</html>