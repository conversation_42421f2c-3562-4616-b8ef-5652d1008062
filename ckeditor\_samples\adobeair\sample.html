<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Replace Textarea by Code - CKEditor Sample</title>
	<meta content="text/html; charset=utf-8" http-equiv="content-type" />
	<script type="text/javascript" src="../../ckeditor.js"></script>
	<link href="../sample.css" rel="stylesheet" type="text/css" />
	<style type="text/css">
		body { margin: 10px ; }
	</style></head>
<body>
	<h1 class="samples">
		CKEditor - Adobe AIR Sample
	</h1>
	<p>
		This is a sample HTML/JavaScript Adobe AIR application with CKEditor with default features.
	</p>
	<p>
		<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		<script type="text/javascript">
		//<![CDATA[

			// Replace the <textarea id="editor"> with an CKEditor
			// instance, using default configurations.
			CKEDITOR.replace( 'editor1' );

		//]]>
		</script>
	</p>
	<div id="footer" style="position:absolute;bottom:0;left:0;right:0;width:100%;padding-bottom:10px;">
		<hr />
		<p>
			CKEditor - The text editor for Internet - <a class="samples" href="#" onclick="window.runtime.flash.net.navigateToURL(new window.runtime.flash.net.URLRequest('http://ckeditor.com/'));return false;">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2011, <a class="samples" href="#" onclick="window.runtime.flash.net.navigateToURL(new window.runtime.flash.net.URLRequest('http://cksource.com/'));return false;">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
