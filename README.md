# 会员管理系统 - 深红色主题版

一个功能完整、设计现代、安全可靠的会员管理系统，采用深红色主题设计，特别适合需要管理付费会员和发布专属内容的业务场景。

## 🌟 系统特色

### 🎨 深红色主题设计
- 统一的深红色渐变背景 (#8B0000 → #DC143C → #B22222)
- 毛玻璃效果和现代化UI设计
- 响应式布局，完美适配各种设备
- 流畅的动画效果和交互体验

### 🔐 安全特性
- 双重身份验证（管理员/普通用户）
- SQL注入防护（预处理语句）
- Session安全管理
- 操作日志记录
- 数据备份与恢复

### 📊 功能模块
- **用户认证系统**：安全登录/退出
- **管理员功能**：用户管理、内容发布、数据备份
- **普通用户功能**：内容查看、个人中心
- **内容管理**：富文本编辑、个人专属内容
- **数据管理**：自动备份、一键恢复

## 🚀 快速开始

### 环境要求
- PHP 8.0+
- MySQL 5.7+
- Web服务器（Apache/Nginx）
- CKEditor（已包含）

### 安装步骤

1. **上传文件**
   ```bash
   # 将所有文件上传到网站根目录
   # 确保 ckeditor 目录完整上传
   ```

2. **设置权限**
   ```bash
   chmod 755 backups/
   chmod 644 *.php
   ```

3. **运行安装程序**
   - 访问：`http://your-domain.com/install/`
   - 填写数据库信息：
     - 主机：localhost
     - 用户名：root
     - 密码：111111（或您的密码）
     - 数据库名：member_system（可自定义）

4. **完成安装**
   - 系统自动创建数据表
   - 生成默认管理员账户：admin / admin123
   - 建议立即修改默认密码

## 📁 文件结构

```
会员管理系统/
├── config.php              # 数据库配置文件
├── index.php               # 登录页面
├── close.php               # 退出功能
├── c.php                   # 管理控制台
├── user.php                # 会员管理
├── add.php                 # 添加会员
├── e.php                   # 用户主页
├── check.php               # 内容查看
├── u.php                   # 内容编辑
├── udel.php                # 清空内容
├── del.php                 # 删除用户
├── backup.php              # 数据备份
├── restore.php             # 数据恢复
├── install/                # 安装程序目录
│   └── index.php           # 安装向导
├── backups/                # 备份文件目录
├── ckeditor/               # 富文本编辑器
└── README.md               # 说明文档
```

## 👥 用户角色

### 🔴 管理员 (ok=1)
- **权限**：所有系统功能
- **功能**：
  - 用户管理（增删改查）
  - 内容发布与编辑
  - 数据备份与恢复
  - 系统统计查看

### 🔵 普通用户 (ok=2)
- **权限**：查看权限
- **功能**：
  - 查看系统公告
  - 查看个人专属内容
  - 个人信息查看

## 🛠️ 主要功能

### 1. 用户认证系统
- **登录验证**：用户名/密码验证
- **权限控制**：基于角色的访问控制
- **会话管理**：安全的Session管理
- **自动跳转**：根据权限自动跳转

### 2. 管理员功能
- **控制台**：系统概览、统计数据
- **用户管理**：添加、删除、搜索用户
- **内容发布**：使用CKEditor发布富文本内容
- **个人内容**：为特定用户分配专属内容

### 3. 内容管理系统
- **富文本编辑**：集成CKEditor
- **内容类型**：
  - 通用内容（系统公告）
  - 个人内容（用户专属）
- **版本控制**：内容更新时间记录

### 4. 数据管理
- **自动备份**：一键创建完整数据备份
- **文件管理**：查看、下载、删除备份文件
- **数据恢复**：从备份文件恢复数据
- **批量操作**：支持批量删除备份

## 🗄️ 数据库结构

### 用户表 (tab)
```sql
CREATE TABLE `tab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `ok` tinyint(1) NOT NULL DEFAULT 2,
  `regtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 内容表 (content)
```sql
CREATE TABLE `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `content` longtext NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'general',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## 🔧 配置说明

### 数据库配置 (config.php)
```php
$db_host = 'localhost';        // 数据库主机
$db_username = 'root';         // 数据库用户名
$db_password = '111111';       // 数据库密码
$db_name = 'member_system';    // 数据库名称
```

### 系统配置
```php
define('SYSTEM_NAME', '会员管理系统');
define('VERSION', '2.0');
define('THEME_COLOR', '#8B0000'); // 深红色主题
```

## 🎯 使用场景

1. **付费会员管理**
   - 管理付费会员的访问权限
   - 向付费会员提供专属内容
   - 区分管理员和普通会员

2. **内容发布平台**
   - 使用富文本编辑器发布内容
   - 为不同用户推送个性化内容
   - 支持各种设备访问

3. **数据管理中心**
   - 定期备份重要数据
   - 快速恢复系统数据
   - 实时查看用户和内容统计

## 🔒 安全建议

1. **安装后立即**：
   - 修改默认管理员密码
   - 删除 install 目录
   - 设置合适的文件权限

2. **定期维护**：
   - 定期备份数据
   - 更新系统密码
   - 检查访问日志

3. **服务器安全**：
   - 使用HTTPS协议
   - 配置防火墙
   - 定期更新PHP版本

## 📞 技术支持

- **系统版本**：v2.0
- **PHP版本**：8.0+
- **数据库**：MySQL 5.7+
- **编辑器**：CKEditor

## 📄 更新日志

### v2.0 (当前版本)
- ✅ 全新深红色主题设计
- ✅ 响应式布局优化
- ✅ 安全性增强
- ✅ 数据备份恢复功能
- ✅ 富文本编辑器集成
- ✅ 用户体验优化

---

**© 2024 会员管理系统 - 深红色主题版**
