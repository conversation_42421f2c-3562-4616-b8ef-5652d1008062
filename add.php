<?php
// 开启会话
session_start();

// 检查用户是否登录且为管理员
if (!isset($_SESSION['user_id']) || $_SESSION['ok'] != 1) {
    header("Location: index.php");
    exit;
}

// 包含数据库配置
require_once 'config.php';

// 处理注册请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $user = $conn->real_escape_string($_POST['user']);
    $pass = md5($_POST['pass']); // 使用MD5加密密码
    $confirm_pass = md5($_POST['confirm_pass']);
    $ok = isset($_POST['ok']) ? intval($_POST['ok']) : 2; // 默认为普通用户
    
    // 验证输入
    if (empty($user) || empty($_POST['pass'])) {
        $error = "用户名和密码不能为空";
    } elseif ($pass != $confirm_pass) {
        $error = "两次输入的密码不一致";
    } else {
        // 检查用户名是否已存在
        $sql = "SELECT id FROM tab WHERE user = '$user'";
        $result = $conn->query($sql);
        
        if ($result->num_rows > 0) {
            $error = "用户名已存在";
        } else {
            // 插入新用户
            $sql = "INSERT INTO tab (user, pass, ok) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssi", $user, $pass, $ok);
            
            if ($stmt->execute()) {
                $success = "会员添加成功";
                // 清空表单
                $user = '';
            } else {
                $error = "会员添加失败: " . $conn->error;
            }
        }
    }
}

// 处理添加会员请求
if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $user_type = (int)($_POST['user_type'] ?? 2);
    
    // 验证输入
    if (empty($username)) {
        $error = '用户名不能为空！';
    } elseif (strlen($username) < 3) {
        $error = '用户名至少需要3个字符！';
    } elseif (strlen($username) > 20) {
        $error = '用户名不能超过20个字符！';
    } elseif (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fa5}]+$/u', $username)) {
        $error = '用户名只能包含字母、数字、下划线和中文！';
    } elseif (empty($password)) {
        $error = '密码不能为空！';
    } elseif (strlen($password) < 6) {
        $error = '密码至少需要6个字符！';
    } elseif ($password !== $confirm_password) {
        $error = '两次输入的密码不一致！';
    } elseif (!in_array($user_type, [1, 2])) {
        $error = '用户类型无效！';
    } else {
        try {
            // 检查用户名是否已存在
            $check_stmt = safe_query($pdo, "SELECT COUNT(*) FROM tab WHERE username = ?", [$username]);
            if ($check_stmt->fetchColumn() > 0) {
                $error = '用户名已存在，请选择其他用户名！';
            } else {
                // 创建新用户
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $insert_stmt = safe_query($pdo, 
                    "INSERT INTO tab (username, password, ok, regtime) VALUES (?, ?, ?, NOW())",
                    [$username, $hashed_password, $user_type]
                );
                
                if ($insert_stmt) {
                    $message = "会员 '$username' 添加成功！";
                    // 清空表单
                    $_POST = [];
                } else {
                    $error = '添加失败，请稍后重试！';
                }
            }
        } catch (Exception $e) {
            $error = '添加失败：' . $e->getMessage();
            error_log("添加用户错误: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 添加会员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
            min-height: 100vh;
            color: #333;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            border-bottom: 3px solid #8B0000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #8B0000;
            font-size: 2.2em;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-links a {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 0, 0, 0.3);
        }
        
        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 0 20px;
        }
        
        .form-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-header h2 {
            color: #8B0000;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #8B0000;
            box-shadow: 0 0 15px rgba(139, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
            transform: scale(1.02);
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
        
        .user-type-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }
        
        .type-option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .type-option:hover {
            border-color: #8B0000;
            transform: translateY(-2px);
        }
        
        .type-option.selected {
            border-color: #8B0000;
            background: rgba(139, 0, 0, 0.1);
        }
        
        .type-option input[type="radio"] {
            display: none;
        }
        
        .type-option .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .type-option .title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .type-option .desc {
            font-size: 12px;
            color: #666;
        }
        
        .btn {
            background: linear-gradient(135deg, #8B0000, #DC143C);
            color: white;
            padding: 18px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(139, 0, 0, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            margin-top: 15px;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .form-tips {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            border-left: 4px solid #8B0000;
        }
        
        .form-tips h4 {
            color: #8B0000;
            margin-bottom: 10px;
        }
        
        .form-tips ul {
            margin-left: 20px;
            color: #666;
        }
        
        .form-tips li {
            margin-bottom: 5px;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                margin: 30px auto;
            }
            
            .form-panel {
                padding: 25px;
            }
            
            .user-type-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>➕ 添加会员</h1>
            <button class="logout-btn" onclick="window.location.href='close.php'">退出登录</button>
        </div>
    </div>
    
    <div class="container">
        <div class="form-panel">
            <div class="form-header">
                <h2>👤 创建新会员</h2>
                <p>为系统添加新的会员账户</p>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="success"><?php echo $success; ?></div>
            <?php elseif (isset($error)): ?>
                <div class="error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form method="post">
                    <label for="user">用户名:</label>
                    <input type="text" id="user" name="user" value="<?php echo isset($user) ? htmlspecialchars($user) : ''; ?>" required>
                    
                    <label for="pass">密码:</label>
                    <input type="password" id="pass" name="pass" required>
                    
                    <label for="confirm_pass">确认密码:</label>
                    <input type="password" id="confirm_pass" name="confirm_pass" required>
                    
                    <label for="ok">权限级别:</label>
                    <select id="ok" name="ok">
                        <option value="2" selected>普通用户</option>
                        <option value="1">管理员</option>
                    </select>
                    
                    <input type="submit" value="添加会员" class="submit-btn">
                </form>
            
        </div>
    </div>
    
    <script>
    </script>
</body>
</html>
