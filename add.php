<?php
/**
 * 会员管理系统 - 添加会员页面
 * 深红色主题版本
 */

// 包含系统配置
require_once 'config.php';

// 检查管理员权限
check_admin();

$error = '';
$success = '';

// 处理添加会员请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $user_type = intval($_POST['user_type'] ?? 2); // 默认为普通用户
    $status = intval($_POST['status'] ?? 1); // 默认为正常状态

    // 验证输入
    if (empty($username)) {
        $error = '用户名不能为空！';
    } elseif (strlen($username) < 3 || strlen($username) > 20) {
        $error = '用户名长度必须在3-20个字符之间！';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $error = '用户名只能包含字母、数字和下划线！';
    } elseif (empty($password)) {
        $error = '密码不能为空！';
    } elseif (strlen($password) < 6) {
        $error = '密码长度不能少于6个字符！';
    } elseif ($password !== $confirm_password) {
        $error = '两次输入的密码不一致！';
    } elseif (!in_array($user_type, [1, 2])) {
        $error = '无效的用户类型！';
    } else {
        try {
            $pdo = connectDB();

            // 检查用户名是否已存在
            $stmt = safe_query($pdo, "SELECT id FROM tab WHERE username = ?", [$username]);
            if ($stmt->fetch()) {
                $error = '用户名已存在，请选择其他用户名！';
            } else {
                // 插入新用户
                $password_md5 = md5($password);
                $stmt = safe_query($pdo,
                    "INSERT INTO tab (username, password, ok, status) VALUES (?, ?, ?, ?)",
                    [$username, $password_md5, $user_type, $status]
                );

                $success = '会员添加成功！用户名：' . $username;
                log_action('添加会员', '用户名: ' . $username . ', 类型: ' . ($user_type == 1 ? '管理员' : '普通用户'));

                // 清空表单数据
                $username = '';
                $user_type = 2;
                $status = 1;
            }
        } catch (Exception $e) {
            $error = '添加会员失败：' . $e->getMessage();
            error_log("添加会员错误: " . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - 添加会员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 0 20px;
        }

        .form-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-header h2 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .form-header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
            transform: scale(1.02);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .user-type-section {
            margin-bottom: 30px;
        }

        .user-type-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .type-option {
            padding: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .type-option:hover {
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .type-option.selected {
            border-color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.2);
        }

        .type-option input[type="radio"] {
            display: none;
        }

        .type-option .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .type-option .title {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .type-option .desc {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 0.9em;
            opacity: 0.8;
        }

        .strength-weak { color: #ffcccb; }
        .strength-medium { color: #ffd700; }
        .strength-strong { color: #90ee90; }

        .btn {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            color: #8B0000;
            padding: 18px 30px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(139, 0, 0, 0.1), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            margin-top: 15px;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border: 1px solid #ef5350;
        }

        .alert-success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .form-tips {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 25px;
            border-left: 4px solid rgba(255, 255, 255, 0.5);
        }

        .form-tips h4 {
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .form-tips ul {
            margin-left: 20px;
            opacity: 0.9;
        }

        .form-tips li {
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .container {
                margin: 30px auto;
            }

            .form-panel {
                padding: 25px;
            }

            .user-type-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>➕ 添加会员</h1>
            <div class="nav-links">
                <a href="c.php" class="nav-link">返回控制台</a>
                <a href="user.php" class="nav-link">会员管理</a>
                <a href="close.php" class="nav-link">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="form-panel">
            <div class="form-header">
                <h2>👤 创建新会员</h2>
                <p>为系统添加新的会员账户</p>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    ⚠️ <?php echo safe_output($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    ✅ <?php echo safe_output($success); ?>
                </div>
            <?php endif; ?>

            <form method="post" id="addUserForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username"
                           placeholder="请输入用户名（3-20个字符）"
                           value="<?php echo safe_output($_POST['username'] ?? ''); ?>"
                           required>
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password"
                           placeholder="请输入密码（至少6个字符）"
                           required>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>

                <div class="form-group">
                    <label for="confirm_password">确认密码</label>
                    <input type="password" id="confirm_password" name="confirm_password"
                           placeholder="请再次输入密码"
                           required>
                </div>

                <div class="user-type-section">
                    <label>用户类型</label>
                    <div class="user-type-options">
                        <label class="type-option" for="type_user">
                            <input type="radio" id="type_user" name="user_type" value="2"
                                   <?php echo ($_POST['user_type'] ?? 2) == 2 ? 'checked' : ''; ?>>
                            <div class="icon">👤</div>
                            <div class="title">普通用户</div>
                            <div class="desc">可以查看内容和使用基本功能</div>
                        </label>

                        <label class="type-option" for="type_admin">
                            <input type="radio" id="type_admin" name="user_type" value="1"
                                   <?php echo ($_POST['user_type'] ?? 2) == 1 ? 'checked' : ''; ?>>
                            <div class="icon">👑</div>
                            <div class="title">管理员</div>
                            <div class="desc">拥有完整的系统管理权限</div>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="status">账户状态</label>
                    <select id="status" name="status">
                        <option value="1" <?php echo ($_POST['status'] ?? 1) == 1 ? 'selected' : ''; ?>>
                            ✅ 正常（可以登录）
                        </option>
                        <option value="0" <?php echo ($_POST['status'] ?? 1) == 0 ? 'selected' : ''; ?>>
                            🚫 禁用（无法登录）
                        </option>
                    </select>
                </div>

                <button type="submit" class="btn">
                    ➕ 创建会员账户
                </button>

                <a href="user.php" class="btn btn-secondary">
                    📋 返回会员列表
                </a>
            </form>

            <div class="form-tips">
                <h4>💡 创建会员提示</h4>
                <ul>
                    <li>用户名只能包含字母、数字和下划线</li>
                    <li>用户名长度必须在3-20个字符之间</li>
                    <li>密码长度不能少于6个字符</li>
                    <li>管理员拥有完整的系统管理权限</li>
                    <li>普通用户只能查看内容和使用基本功能</li>
                    <li>禁用状态的账户无法登录系统</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 用户类型选择
        document.querySelectorAll('.type-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.type-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type="radio"]').checked = true;
            });
        });

        // 初始化选中状态
        document.querySelectorAll('input[name="user_type"]').forEach(radio => {
            if (radio.checked) {
                radio.closest('.type-option').classList.add('selected');
            }
        });

        // 密码强度检测
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');

            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }

            let strength = 0;
            let feedback = [];

            if (password.length >= 6) strength++;
            else feedback.push('至少6个字符');

            if (/[a-z]/.test(password)) strength++;
            else feedback.push('包含小写字母');

            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('包含大写字母');

            if (/[0-9]/.test(password)) strength++;
            else feedback.push('包含数字');

            if (/[^a-zA-Z0-9]/.test(password)) strength++;
            else feedback.push('包含特殊字符');

            if (strength < 2) {
                strengthDiv.className = 'password-strength strength-weak';
                strengthDiv.textContent = '密码强度：弱 - 建议' + feedback.slice(0, 2).join('、');
            } else if (strength < 4) {
                strengthDiv.className = 'password-strength strength-medium';
                strengthDiv.textContent = '密码强度：中等';
            } else {
                strengthDiv.className = 'password-strength strength-strong';
                strengthDiv.textContent = '密码强度：强';
            }
        });

        // 表单验证
        document.getElementById('addUserForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (username.length < 3 || username.length > 20) {
                e.preventDefault();
                alert('用户名长度必须在3-20个字符之间！');
                return false;
            }

            if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                e.preventDefault();
                alert('用户名只能包含字母、数字和下划线！');
                return false;
            }

            if (password.length < 6) {
                e.preventDefault();
                alert('密码长度不能少于6个字符！');
                return false;
            }

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('两次输入的密码不一致！');
                return false;
            }

            return true;
        });

        // 实时密码确认验证
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.style.borderColor = '#ff6b6b';
            } else {
                this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            }
        });
    </script>
</body>
</html>
