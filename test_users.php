<?php
// 测试用户管理
require_once 'config.php';

try {
    $pdo = connectDB();
    
    echo "<h1>🔍 用户列表</h1>";
    echo "<style>body{font-family:Arial;padding:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;}</style>";
    
    // 显示所有用户
    $users = $pdo->query("SELECT * FROM tab ORDER BY id")->fetchAll();
    
    if (empty($users)) {
        echo "<p>❌ 没有找到任何用户</p>";
        echo "<p>请先运行安装程序或添加用户</p>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>用户名</th><th>权限</th><th>注册时间</th><th>操作</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . ($user['ok'] == 1 ? '👑 管理员' : '🎯 普通用户') . "</td>";
            echo "<td>" . $user['regtime'] . "</td>";
            echo "<td>";
            echo "<a href='u.php?username=" . urlencode($user['username']) . "' style='color:blue;'>📝 编辑内容</a> | ";
            echo "<a href='check.php?username=" . urlencode($user['username']) . "' style='color:green;'>👁️ 查看内容</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 如果没有用户名为111的用户，创建一个
    $user111 = $pdo->prepare("SELECT * FROM tab WHERE username = ?");
    $user111->execute(['111']);
    
    if (!$user111->fetch()) {
        echo "<hr>";
        echo "<h2>➕ 创建测试用户</h2>";
        
        if ($_POST && isset($_POST['create_user'])) {
            try {
                $password = password_hash('123456', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO tab (username, password, ok) VALUES (?, ?, 2)");
                $stmt->execute(['111', $password]);
                echo "<p style='color:green;'>✅ 用户 '111' 创建成功！密码：123456</p>";
                echo "<p><a href='u.php?username=111' style='color:blue;font-weight:bold;'>🚀 立即编辑用户111的内容</a></p>";
            } catch (Exception $e) {
                echo "<p style='color:red;'>❌ 创建失败：" . $e->getMessage() . "</p>";
            }
        } else {
            echo "<form method='POST'>";
            echo "<p>用户名为 '111' 的用户不存在，是否创建？</p>";
            echo "<button type='submit' name='create_user' style='background:#007cba;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;'>创建用户111</button>";
            echo "</form>";
        }
    } else {
        echo "<hr>";
        echo "<p style='color:green;'>✅ 用户 '111' 已存在</p>";
        echo "<p><a href='u.php?username=111' style='color:blue;font-weight:bold;font-size:18px;'>🚀 编辑用户111的内容</a></p>";
    }
    
    echo "<hr>";
    echo "<h2>🔗 快速链接</h2>";
    echo "<a href='index.php' style='color:blue;'>🔑 登录页面</a> | ";
    echo "<a href='add.php' style='color:blue;'>➕ 添加用户</a> | ";
    echo "<a href='user.php' style='color:blue;'>👥 用户管理</a> | ";
    echo "<a href='c.php' style='color:blue;'>🏛️ 管理控制台</a>";
    
} catch (Exception $e) {
    echo "<p style='color:red;'>❌ 数据库错误：" . $e->getMessage() . "</p>";
    echo "<p>请检查数据库配置或重新运行安装程序</p>";
}
?>
