-- 会员管理系统数据备份
-- 备份时间: 2025-07-05 22:38:44
-- 备份人员: admin

SET FOREIGN_KEY_CHECKS=0;

-- 用户表备份
DROP TABLE IF EXISTS `tab`;
CREATE TABLE `tab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `ok` tinyint(1) NOT NULL DEFAULT 2,
  `regtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `tab` (`id`, `username`, `password`, `ok`, `regtime`) VALUES
(1, 'admin', '$2y$10$gQcVEi2KCa.ZdJ0pvezeK.yJsz7GEh9BnN4Y45TmCvYRKZAp225wC', 1, '2025-07-05 22:35:46'),
(2, '111', '$2y$10$5WHc02YMe3ZYvBdG4b7EseQ2j7Vwo6zjdyF39N806tbcP.cw9PbCy', 2, '2025-07-05 22:36:08'),
(3, '222', '$2y$10$m5ZNM9pONUPA9yrMuTdSpeCUoI4cPZ/yR4WTYQ.iPT06OriKKre76', 1, '2025-07-05 22:36:18');

-- 内容表备份
DROP TABLE IF EXISTS `content`;
CREATE TABLE `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `content` longtext NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'general',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `username` (`username`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `content` (`id`, `username`, `content`, `type`, `created_time`, `updated_time`) VALUES
(1, '222', '<p>
	22222222</p>
', 'personal', '2025-07-05 22:36:31', '2025-07-05 22:36:31'),
(2, '111', '<p>
	11111</p>
', 'personal', '2025-07-05 22:36:40', '2025-07-05 22:36:40');

SET FOREIGN_KEY_CHECKS=1;
