<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
Copyright (c) 2003-2011, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>ASP integration Samples List &mdash; CKEditor</title>
	<link type="text/css" rel="stylesheet" href="../sample.css" />
</head>
<body>
	<h1 class="samples">
		CKEditor Samples List for ASP &mdash; CKEditor Sample
	</h1>
	<h2 class="samples">
		Overview
	</h2>
	<p>The ckeditor.asp file provides a wrapper to ease the work of creating CKEditor instances from classic Asp.</p>
	<p>To use it, you must first include it into your page:
	<code>
		&lt;!-- #INCLUDE file="../../ckeditor.asp" --&gt;
	</code>
	Of course, you should adjust the path to make it point to the correct location, and maybe use a full path (with virtual="" instead of file="")
	</p>
	<p>After that script is included, you can use it in different ways, based on the following pattern:</p>

<ol>
	<li>
		Create an instance of the CKEditor class:
<pre class="samples">dim editor
set editor = New CKEditor</pre>
	</li>
	<li>
		Set the path to the folder where CKEditor has been installed, by default it will use /ckeditor/
		<pre class="samples">editor.basePath = "../../"</pre>
	</li>
	<li>
	Now use one of the three main methods to create the CKEditor instances:
	<ul class="samples">
		<li>
				Replace textarea with id (or name) "editor1".
			<pre class="samples">editor.replaceInstance "editor1"</pre>
		</li>
		<li>
			Replace all textareas with CKEditor.
			<pre class="samples">editor.replaceAll empty</pre>
		</li>
		<li>
			Create a textarea element and attach CKEditor to it.
			<pre class="samples">editor.editor "editor1", initialValue</pre>
		</li>
	</ul>
	</li>
</ol>
<p>Before step 3 you can use a number of methods and properties to adjust the behavior of this class and the CKEditor instances
that will be created:</p>
<ul class="samples">
	<li>returnOutput : if set to true, the functions won't dump the code with response.write, but instead they will return it so
	you can do anything you want</li>
	<li>basePath: location of the CKEditor scripts</li>
	<li>initialized: if you set it to true, it means that you have already included the CKEditor.js file into the page and it
		doesn't have to be generated again.</li>
	<li>textareaAttributes: You can set here a Dictionary object with the attributes that you want to output in the call to the "editor" method.</li>

	<li>config: Allows to set config values for all the instances from now on.</li>
	<li>instanceConfig: Allows to set config values just for the next instance.</li>

	<li>addEventHandler: Adds an event handler for all the instances from now on.</li>
	<li>addInstanceEventHandler: Adds an event handler just for the next instance.</li>
	<li>addGlobalEventHandler: Adds an event handler for the global CKEDITOR object.</li>

	<li>clearEventHandlers: Removes one or all the event handlers from all the instances from now on.</li>
	<li>clearInstanceEventHandlers: Removes one or all the event handlers  from the next instance.</li>
	<li>clearGlobalEventHandlers: Removes one or all the event handlers  from the global CKEDITOR object.</li>

</ul>

	<h2 class="samples">
		Basic Samples
	</h2>
	<ul class="samples">
		<li><a class="samples" href="replace.asp">Replace existing textareas by code</a></li>
		<li><a class="samples" href="replaceall.asp">Replace all textareas by code</a></li>
		<li><a class="samples" href="standalone.asp">Create instances in asp</a></li>
	</ul>
	<h2 class="samples">
		Advanced Samples
	</h2>
	<ul class="samples">
		<li><a class="samples" href="advanced.asp">Advanced example</a></li>
		<li><a class="samples" href="events.asp">Listening to events</a></li>
	</ul>
	<div id="footer">
		<hr />
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2011, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
